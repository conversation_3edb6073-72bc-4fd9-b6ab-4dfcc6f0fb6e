<!DOCTYPE html>
<html lang="en">

<head>
    <title><?php echo $this->lang->line('savsoft_quiz'); ?></title>
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0"> -->
    <meta charset="utf-8" name="viewport" content="width=device-width, user-scalable=no" />
    <link rel="stylesheet" href="<?php echo base_url(); ?>public/css/app.css">
    <link rel="stylesheet" href="<?php echo base_url(); ?>css/plugins/jcider.css">
    <link rel="stylesheet" href="<?php echo base_url(); ?>css/plugins/HoldOn.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href='https://fonts.googleapis.com/css?family=Inter' rel='stylesheet'>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

    <!-- <link rel="stylesheet" type="text/css" href="https://www.exam.ptfinalexam.com/assets/backend/css/custom.css"> -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script src="<?php echo base_url(); ?>js/jquery.js"></script>
    <script src="<?php echo base_url(); ?>js/plugins/jcider.min.js"></script>
    <script src="<?php echo base_url(); ?>public/js/app.js"></script>
    <script src="<?php echo base_url(); ?>js/plugins/parsley.js"></script>
    <script src="<?php echo base_url(); ?>js/plugins/notify.min.js"></script>
    <script src="<?php echo base_url(); ?>js/plugins/HoldOn.min.js"></script>
    <script src="<?php echo base_url(); ?>js/plugins/datepicker.js"></script>
    <script src="https://cdn.amplitude.com/libs/analytics-browser-2.11.1-min.js.gz"></script>
    <script src="https://cdn.amplitude.com/libs/plugin-session-replay-browser-1.6.22-min.js.gz"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tarekraafat/autocomplete.js@10.2.9/dist/autoComplete.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@polar-sh/checkout@0.1/dist/embed.global.js" defer data-auto-init></script>
    <script>
        window.amplitude.add(window.sessionReplay.plugin({
            sampleRate: 1
        }));
        window.amplitude.init('9a4d2da56616c26ad4ed4e41eeb81079', {
            "autocapture": {
                "elementInteractions": true
            }
        });
    </script>
    <script>
        BASE_URL = "<?php echo base_url(); ?>";
    </script>
    <noscript>
        <div class="noscript-page text-center">
            <div class="error-content">
                <h3>Your browser does not support JavaScript!</h3>
            </div>
        </div>
    </noscript>
    <style>
        :root {
            --color-green-primary: #12705B;
            --color-green-400: #31C48D;
            --color-green-500: #0E9F6E;
            --color-green-200: #BCF0DA;
            --color-green-700: #046C4E;
            --color-green-50: #F3FAF7;
        }

        .next-btn.active {
            background-color: var(--color-green-primary) !important;
        }

        .cert-item.selected {
            border-color: var(--color-green-primary) !important;
        }

        .cert-item.selected::before {
            background: url(/images/itcertprep/selected.png) no-repeat top right;
            background-size: 20px 20px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            color: var(--color-green-primary) !important;
            border-left: 1px solid #D1D5DB;
            /* border-right: 1px solid #D1D5DB; */
            background-color: var(--color-green-100) !important;

        }

        .dataTables_wrapper .dataTables_filter input:focus {
            box-shadow: none;
            border-color: var(--color-green-400);
        }

        .datepicker-controls button:disabled {
            background-color: #fff !important;
        }

        #dropdownRadioButton.active,
        #dropdownRadioButton2.active,
        .bg-primary-blue-1 {
            background-color: var(--color-green-primary) !important;
        }

        .focus\:ring-blue-200:focus {
            --tw-ring-color: var(--color-green-200) !important;
        }

        .hover\:bg-\[\#EBF5FF\]:hover .bg-primary-blue-1,
        .bg-blue-700 {
            background-color: #12705B !important;
        }

        #submit_button.saveButton:hover {
            background-color: var(--color-green-400) !important;
            outline-color: var(--color-green-200);
        }

        .hover\:bg-\[\#EBF5FF\]:hover {
            background-color: var(--color-green-50) !important;
        }

        .text-primary-blue-1 {
            color: #12705B !important;
        }

        .hover\:bg-blue-400:hover {
            background-color: var(--color-green-400) !important;
        }

        .bg-blue-600,
        .bg-blue-500 {
            background-color: var(--color-green-primary) !important;
        }

        .focus\:ring-blue-300:focus {
            --tw-ring-color: transparent;
        }

        .hover\:ring-blue-300:hover {
            --tw-ring-color: var(--color-green-200) !important;
        }

        .focus\:border-blue-500:focus {
            border-color: #12705B;
        }

        .peer:checked~.peer-checked\:bg-blue-600 {
            background-color: #12705B;
        }

        [type='text']:focus,
        [type='email']:focus,
        [type='password']:focus,
        [type='number']:focus,
        select:focus {
            --tw-ring-color: #12705B;
            border-color: #12705B;
        }

        .basic_container .num_item.selected .num_text {
            color: #12705B;
        }

        .basic_container .num_item.selected input {
            border-color: #12705B;
        }

        .advance_container .domain_item.selected {
            border-color: #12705B;
            color: #12705B;
            background-color: #EBF5FF;
        }

        .text-blue-600,
        .hover\:text-blue-600:hover {
            color: #12705B;
        }

        .navigator-section .next-btn,
        .navigator-section .back-btn,
        .navigator-section .result-btn {
            color: #12705B;
            border-color: #12705B;
        }

        .navigator-section .next-btn img,
        .navigator-section .back-btn img,
        .navigator-section .result-btn img,
        .previous-question img,
        .next-question img {
            filter: brightness(0) saturate(100%) invert(49%) sepia(36%) saturate(926%) hue-rotate(153deg) brightness(94%) contrast(100%);
        }

        [type='radio'] {
            color: #12705B;
        }

        .border-blue-600,
        .border-primary-blue-1 {
            border-color: #12705B;
        }

        .mark-active-indicator,
        .active-indicator {
            background-color: #12705B !important;
        }

        .text-\[\#4776ED\] {
            color: var(--color-green-primary) !important;
        }

        .text-\[\#4776ED\]:hover {
            color: var(--color-green-400) !important;
        }

        .text-\[\#4776ED\]:focus {
            color: var(--color-green-700) !important;
        }

        [type='checkbox']:focus,
        [type='radio']:focus {
            --tw-ring-color: var(--color-green-400) !important;
            border-color: var(--color-green-400) !important;
        }

        .custom-input:checked {
            background-color: var(--color-green-primary) !important;
        }

        .showNoti {
            opacity: 0;
            transition: 0.5s opacity;
            display: flex;
        }

        /* Polar Payment Popup Styles */
        #polarPaymentPopup {
            transition: opacity 0.3s ease-in-out;
        }

        #polarPaymentPopup>div {
            transition: height 0.3s ease-in-out;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        #closePolarPopup {
            transition: all 0.2s;
        }

        #closePolarPopup:hover {
            background-color: #f3f4f6;
            transform: scale(1.05);
        }

        li.step-done::after {
            background: #1C64F2;
        }

        .bundle-includes {
            max-height: calc(100vh - 450px);
            overflow-y: auto;
        }
    </style>
</head>

<body class="w-screen h-screen relative overflow-x-hidden">
    <div id="error_message" class="showNoti z-[100] absolute flex items-start top-[25px] right-[25px] w-[380px] bg-[#FE4D01] px-[16px] py-[12px] rounded-[4px]">
        <img class="mr-[10px]" src="<?php echo base_url(); ?>images/icons/Close-icon.svg">
        <p id="error_text" class="text-[14px] text-white w-[290px]"></p>
        <img onclick="closeNotification()" src="<?php echo base_url(); ?>images/icons/x.svg" class="ml-[12px] cursor-pointer">
    </div>

    <div id="success_message" class="showNoti z-50 absolute flex items-start top-[25px] right-[25px] w-[380px] bg-[#0066D3] px-[16px] py-[12px] rounded-[4px]">
        <img class="mr-[10px]" src="<?php echo base_url(); ?>images/icons/info-circle.svg">
        <p id="success_text" class="text-[14px] text-white w-[290px]"></p>
        <img onclick="closeNotification()" src="<?php echo base_url(); ?>images/icons/x.svg" class="ml-[12px] cursor-pointer">
    </div>
    <!-- Header -->
    <div class="container ml-auto mr-auto w-full flex items-center p-4">
        <div class="w-3/12">
            <div class="logo">
                <a href="<?php echo base_url(); ?>">
                    <img src="<?php echo base_url(); ?>images/new/itcertprep/logo_with_text.svg" width="160" alt="logo">
                </a>
            </div>
        </div>
        <div class="w-6/12">
            <ol class="lg:flex items-center w-full text-sm text-gray-900 progress-bar font-medium sm:text-base hidden">
                <li class="flex w-full relative text-gray-400 progress-1 current-step items-center after:content-['']  after:w-full after:h-0.5  after:bg-gray-200 after:inline-block after:absolute after:top-[2.35rem] after:left-[52%]">
                    <div class="block whitespace-nowrap z-10 text-center w-full">
                        Choose certificate
                        <img src="<?= base_url() ?>images/icons/step-current.svg" alt="step-current" class="step-current mx-auto pt-2">
                        <img src="<?= base_url() ?>images/itcertprep/check_done.svg" alt="step-done" class="step-done mx-auto hidden pt-2">
                    </div>
                </li>
                <li class="flex w-full relative text-gray-400 progress-2 after:content-['']  after:w-full after:h-0.5  after:bg-gray-200 after:inline-block after:absolute after:top-[2.35rem] after:left-[52%]">
                    <div class="block whitespace-nowrap z-10 text-center w-full">
                        Fill information
                        <img src="<?= base_url() ?>images/icons/step-current.svg" alt="step-current" class="step-current mx-auto hidden pt-2">
                        <img src="<?= base_url() ?>images/itcertprep/check_done.svg" alt="step-done" class="step-done hidden mx-auto pt-2 bg-white">
                        <img src="<?= base_url() ?>images/icons/next-step.svg" alt="next-step" class="next-step mx-auto pt-2">
                    </div>
                </li>
                <li class="flex w-full relative text-gray-400 progress-3">
                    <div class="block whitespace-nowrap z-10 text-center w-full">
                        Checkout
                        <img src="<?= base_url() ?>images/icons/step-current.svg" alt="step-current" class="step-current hidden mx-auto pt-2">
                        <img src="<?= base_url() ?>images/icons/next-step.svg" alt="next-step" class="next-step mx-auto pt-2">
                    </div>
                </li>
            </ol>
        </div>
        <div class="w-3/12 flex justify-end">
            <a id="help-button" class="flex cursor-pointer" href="https://m.me/scrumpassvn" target="_blank">
                <img src="<?php echo base_url(); ?>images/itcertprep/Help.svg" alt="Help">
                <span class="text-sm font-medium text-primary-blue-1 ml-1">Support</span>
            </a>
        </div>
    </div>
    <!-- End Header -->
    <div class="container ml-auto mr-auto main-content p-4 lg:pt-6 ">
        <!-- Mobile Progress bar -->
        <div class="w-full lg:hidden h-14 progress-bar mb-6">
            <ol class="flex items-center w-full text-xs text-gray-900 font-medium">
                <li class="flex justify-start w-full relative text-gray-400 progress-1 current-step items-center after:content-['']  after:w-[115%] after:h-0.5  after:bg-gray-200 after:inline-block after:absolute after:top-[2.85rem] after:left-[32%]">
                    <div class="block z-10 text-center w-16">
                        Choose certificate
                        <img src="<?= base_url() ?>images/icons/step-current.svg" alt="step-current" class="step-current mx-auto pt-2">
                        <img src="<?= base_url() ?>images/itcertprep/check_done.svg" alt="step-done" class="step-done mx-auto hidden pt-2">
                    </div>
                </li>
                <li class="flex justify-center w-full relative text-gray-400 progress-2 after:content-['']  after:w-[115%] after:h-0.5  after:bg-gray-200 after:inline-block after:absolute after:top-[2.85rem] after:left-[52%]">
                    <div class="block z-10 text-center w-16">
                        Fill information
                        <img src="<?= base_url() ?>images/icons/step-current.svg" alt="step-current" class="step-current mx-auto hidden pt-2">
                        <img src="<?= base_url() ?>images/itcertprep/check_done.svg" alt="step-done" class="step-done hidden mx-auto pt-2 bg-white">
                        <img src="<?= base_url() ?>images/icons/next-step.svg" alt="next-step" class="next-step mx-auto pt-2">
                    </div>
                </li>
                <li class="flex justify-end w-full relative text-gray-400 progress-3">
                    <div class="block z-10 text-center w-16">
                        Checkout
                        <img src="<?= base_url() ?>images/icons/step-current.svg" alt="step-current" class="step-current hidden mx-auto pt-2">
                        <img src="<?= base_url() ?>images/icons/next-step.svg" alt="next-step" class="next-step mx-auto pt-2">
                    </div>
                </li>
            </ol>
        </div>
        <!-- Main Content -->
        <!-- Step 1 -->
        <div class="step-1 flex flex-col lg:flex-row gap-[30px] step-content">
            <div class="certificate-content w-full lg:w-8/12">
                <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-5">
                    <span class="text-lg font-bold mb-4 lg:mb-0">List of certificates</span>
                    <div class="search-box relative lg:w-[300px] w-full">
                        <img src="<?= base_url() ?>/images/icons/search.svg" alt="search" class="absolute top-3.5 left-4">
                        <svg class="absolute top-3.5 left-4" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.75 15.75L11.25 11.25M12.75 7.5C12.75 8.18944 12.6142 8.87213 12.3504 9.50909C12.0865 10.146 11.6998 10.7248 11.2123 11.2123C10.7248 11.6998 10.146 12.0865 9.50909 12.3504C8.87213 12.6142 8.18944 12.75 7.5 12.75C6.81056 12.75 6.12787 12.6142 5.49091 12.3504C4.85395 12.0865 4.2752 11.6998 3.78769 11.2123C3.30018 10.7248 2.91347 10.146 2.64963 9.50909C2.3858 8.87213 2.25 8.18944 2.25 7.5C2.25 6.10761 2.80312 4.77226 3.78769 3.78769C4.77226 2.80312 6.10761 2.25 7.5 2.25C8.89239 2.25 10.2277 2.80312 11.2123 3.78769C12.1969 4.77226 12.75 6.10761 12.75 7.5Z" stroke="#1C64F2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <input type="text" id="autoComplete" placeholder="Looking for a specific certificate?" class="lg:w-[300px] w-full text-sm font-normal text-gray-500 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 pl-10">
                    </div>
                </div>
                <div class="flex items-center mb-3">
                    <span class="text-sm font-medium text-gray-500 w-full number-category"></span>
                    <span class="text-sm font-medium text-gray-900 mr-2 hidden lg:block w-20">Field</span>
                    <button id="dropdownRadioButton" data-dropdown-toggle="dropdownRadio" class="flex justify-between items-center focus:ring-2 focus:ring-blue-200 text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 font-medium rounded-lg text-xs px-3 py-2 w-44 h-8 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600" type="button">
                        <span id="current_type" class="overflow-hidden text-ellipsis inline-block whitespace-nowrap">All</span>
                        <svg class="w-3 h-3 ml-2" aria-hidden="true" fill="none" stroke="#000000" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <!-- Dropdown menu -->
                    <div id="dropdownRadio" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700 dark:divide-gray-600" data-popper-reference-hidden="" data-popper-escaped="" data-popper-placement="top" style="position: absolute; inset: auto auto 0px 0px; margin: 0px; transform: translate3d(522.5px, 3847.5px, 0px);">
                        <ul class="py-3 space-y-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownRadioButton">
                            <li class="cursor-pointer">
                                <div class="flex items-center p-2 rounded hover:bg-[#EBF5FF]">
                                    <input checked="" id="all_category" type="radio" value=0 title="Tất cả" name="filter-radio" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600 cursor-pointer">
                                    <label for="all_category" class="w-full ml-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300 cursor-pointer">Tất cả</label>
                                </div>
                            </li>
                            <?php foreach ($cert_categories as $category): ?>
                                <li class="cursor-pointer">
                                    <div class="flex items-center p-2 rounded hover:bg-[#EBF5FF]">
                                        <input id="<?= $category['id'] ?>" type="radio" value=<?= $category['id'] ?> name="filter-radio" title="<?= $category['eng_name'] ?>" class="cursor-pointer w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="<?= $category['id'] ?>" class="cursor-pointer w-full ml-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300"><?= $category['eng_name'] ?></label>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
                <div class="empty-list w-64 mx-auto lg:w-full flex flex-col items-center hidden mt-14">
                    <img src="<?= base_url() ?>/images/icons/no_result.svg" alt="No result">
                    <span class="text-sm font-medium text-gray-800">Can't find any certificate</span>
                </div>

                <div class="grid lg:grid-cols-5 grid-cols-3 gap-2 lg:gap-4 overflow-y-auto overflow-x-hidden certificate-list pr-2">
                    <!-- Certificate items for desktop & tablet device will be loaded here -->
                </div>

                <div class="swiper certSwiper">
                    <div class="swiper-wrapper certificate-list-mobile">
                        <!-- Certificate items for mobile device will be loaded here -->
                    </div>
                </div>
                <div class="swiper-pagination"></div>
            </div>
            <div class="package-info w-full lg:w-4/12">
                <div class="lg:p-6 p-4 border rounded-2xl border-gray-200">
                    <h2 class="text-base font-bold text-gray-900 mb-4">Select Package</h2>

                    <div class="">
                        <label class="flex items-start mb-4 cursor-pointer plan-item">
                            <div class="text-gray-900 w-full">
                                <div class="mb-1 flex items-center justify-between">
                                    <div class="">
                                        <input type="radio" id="1month_option" name="plan" class="cursor-pointer mr-2 border-gray-400 disabled:bg-gray-200 disable:border-gray-300" disabled />
                                        <span class="text-sm text-gray-900 font-medium">1 Month <span class="lg:hidden text-sm font-bold mobile-lite-price"></span></span>
                                    </div>
                                    <div class="text-sm font-bold text-gray-900">$12,99</div>
                                </div>
                                <div class="flex justify-between">
                                    <div class="text-sm text-gray-500">Billed monthly</div>
                                    <div class="text-sm font-medium">$12,99/month</div>
                                </div>
                            </div>
                        </label>

                        <label class="flex items-start cursor-pointer plan-item">
                            <div class="text-gray-900 w-full">
                                <div class="mb-1 flex items-center justify-between">
                                    <div class="">
                                        <input type="radio" id="12months_option" name="plan" class="cursor-pointer mr-2 border-gray-400 disabled:bg-gray-200 disable:border-gray-300" disabled />
                                        <span class="text-sm text-gray-900 font-medium">12 Months<span class="lg:hidden text-sm font-bold mobile-full-price"></span></span>
                                    </div>
                                    <div class="text-sm font-bold text-gray-900"><span class="text-sm font-medium text-gray-600 line-through"></span>$99,99</div>
                                </div>
                                <div class="flex justify-between">
                                    <div class="text-sm text-gray-500">Billed yearly</div>
                                    <div class="text-sm font-medium">$8,3/month</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    <button class="lg:hidden bg-primary-blue-1 p-3 text-white text-base font-medium next-btn w-full rounded-lg">Tiếp tục</button>
                </div>

                <div class="mb-4 mt-4">
                    <h3 class="text-xs font-bold text-gray-900 mb-2">This bundle includes:</h3>
                    <ul class="space-y-2 bundle-includes">
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-1 p-1">
                                <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                            </div>
                            <div class="ml-1">
                                <span class="text-xs font-semibold text-gray-900">Full access</span>
                                <span class="text-xs text-gray-600"> to study materials from <span class="font-semibold">IT Cert Prep</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-1 p-1">
                                <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                            </div>
                            <div class="ml-1">
                                <span class="text-xs font-semibold text-gray-900">A diverse question bank,</span>
                                <span class="text-xs text-gray-600"> featuring carefully selected questions compiled from multiple reputable sources</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-1 p-1">
                                <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                            </div>
                            <div class="ml-1">
                                <span class="text-xs font-semibold text-gray-900">Learner-started personalized design,</span>
                                <span class="text-xs text-gray-600"> tailored to each individual's learning journey</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-1 p-1">
                                <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                            </div>
                            <div class="ml-1">
                                <span class="text-xs font-semibold text-gray-900">Exam pass rate indicator</span>
                                <span class="text-xs text-gray-600"> — estimates your chance of passing the certification.</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-1 p-1">
                                <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                            </div>
                            <div class="ml-1">
                                <span class="text-xs font-semibold text-gray-900">Advanced practice mode</span>
                                <span class="text-xs text-gray-600"> for deep skill reinforcement and targeted improvement.</span>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mt-1 p-1">
                                <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                            </div>
                            <div class="ml-1">
                                <span class="text-xs font-semibold text-gray-900">Domain-based performance analytics,</span>
                                <span class="text-xs text-gray-600"> helping you assess and track your proficiency in each knowledge area.</span>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- End Step 1 -->

        <!-- Step 2 -->
        <div class="step-2 hidden step-content lg:px-4 mb-20 lg:mb-0">
            <div class="p-1 lg:p-0">
                <div class="flex flex-col lg:flex-row gap-[30px]">
                    <!-- Main Content - Left Side -->
                    <div class="w-full lg:w-8/12">
                        <h2 class="text-lg font-bold text-gray-900 mb-4">Account information</h2>
                        <p class="text-base font-medium text-gray-900 mb-4">Already have an account?</p>
                        <div id="loginToEnrollBtn" class="w-full border border-gray-200 rounded-lg p-3 font-medium text-base text-gray-800 text-center cursor-pointer">
                            Log in to enroll
                        </div>
                        <div class="flex items-center w-full my-4">
                            <div class="flex-grow h-px bg-gray-200"></div>
                            <span class="mx-4 text-gray-400 font-medium">OR</span>
                            <div class="flex-grow h-px bg-gray-200"></div>
                        </div>

                        <h3 class="text-base font-medium text-gray-900 mb-4">Create Account</h3>

                        <form id="signup-form" class="mb-6">
                            <div class="space-y-8">
                                <!-- First row of inputs -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <div class="w-full">
                                        <label for="fullname" class="block text-sm font-medium text-gray-900 mb-2">Full name <span class="text-red-600">*</span></label>
                                        <input type="text" id="fullname" name="full_name" placeholder="Your name is?" required
                                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-2">
                                        <p class="text-xs text-red-600 min-h-[18px] error-message mt-1"></p>
                                    </div>
                                    <div class="w-full">
                                        <label for="email" class="block text-sm font-medium text-gray-900 mb-2">Email <span class="text-red-600">*</span></label>
                                        <input type="email" id="email" name="email" placeholder="Learning account email" required
                                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-2">
                                        <p class="text-xs text-red-600 min-h-[18px] error-message mt-1"></p>
                                    </div>
                                </div>

                                <!-- Second row of inputs -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <div class="w-full">
                                        <label for="referral_source" class="block text-sm font-medium text-gray-900 mb-2">How did you hear about IT Cert Prep? <span class="text-red-600">*</span></label>
                                        <div class="relative">
                                            <select id="referral_source" name="source" required
                                                class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-500 focus:ring-2 appearance-none">
                                                <option value="" selected disabled>Source of referral</option>
                                                <option value="friend_recommendation">Friend Recommendation</option>
                                                <option value="facebook_ads">Ads/Page Facebook</option>
                                                <option value="group_shares">Group Shares</option>
                                                <option value="google_website">Google and Website</option>
                                                <option value="planning_poker">Planning Poker</option>
                                                <option value="tiktok_ads">Tiktok</option>
                                                <option value="linkedin_ads">Linkedin</option>
                                                <option value="other">Other</option>
                                            </select>
                                            <p class="text-xs text-red-600 min-h-[18px] error-message mt-1"></p>
                                        </div>
                                    </div>
                                    <div class="w-full">
                                        <label for="exam_date" class="block text-sm font-medium text-gray-900 mb-2">Planned exam date</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                                                <svg id="iconDate" aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <input datepicker datepicker-autohide datepicker-format="dd/mm/yyyy" readonly type="text" name="exam-date"
                                                value="<?php echo date('d/m/Y', strtotime('+1 month')); ?>"
                                                class="w-full text-sm pl-10 px-4 py-3 border border-gray-200 bg-gray-50 rounded-md focus:border-[#1C64F2] focus:ring-[#1C64F2] focus:outline-none focus:ring-1" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6">
                                <label class="flex items-center">
                                    <input type="checkbox" id="terms_agreement" name="terms_agreement" required
                                        class="w-4 h-4 text-primary-blue-1 border-gray-300 rounded focus:ring-blue-300">
                                    <span class="ml-2 text-sm font-medium text-gray-900">
                                        I agree to the <a href="https://itcertprep.com/privacy-policy/" target="_blank" class="text-primary-blue-1 hover:text-blue-400">Privacy Policy</a> and <a href="https://itcertprep.com/terms-of-use/" target="_blank" class="text-primary-blue-1 hover:text-blue-400">Term of use</a>.
                                    </span>
                                </label>
                                <div class="text-xs text-red-600 min-h-[18px] term-error-message mt-1"></div>
                            </div>
                        </form>

                        <div class="mt-6 lg:hidden">
                            <button type="button" id="mobile-continue-btn" class="w-full bg-primary-blue-1 text-white py-3 px-4 rounded-lg font-medium">
                                Continue
                            </button>
                        </div>
                    </div>

                    <!-- Right Side - Package Info -->
                    <div class="w-full lg:w-4/12">
                        <div class="border border-gray-200 rounded-2xl p-4 lg:p-6">
                            <h3 class="text-base font-bold text-gray-900 mb-2"><span class="selected-cert-name"></span> Bundle</h3>

                            <!-- Package Selection -->
                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-1">
                                    <div class="flex items-center">
                                        <label for="plan_12months" class="text-sm font-medium text-gray-900 plan-duration">12 Months</label>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <span class="text-sm font-bold text-gray-900 plan-price">$960</span>
                                    </div>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Billed yearly</span>
                                    <span class="font-medium text-gray-900"><span class="plan-monthly-price">80</span>/month</span>
                                </div>
                            </div>

                            <div class="h-px bg-gray-200 my-4"></div>

                            <!-- Savings Info -->
                            <div class="space-y-2">
                                <!-- <div class="flex justify-between text-sm">
                                    <span class="text-blue-600">Savings</span>
                                    <span class="text-blue-600">-$240</span>
                                </div> -->
                                <div class="flex justify-between">
                                    <span class="text-base font-medium text-gray-900">Total</span>
                                    <span class="text-base font-bold text-gray-900 plan-price">$960</span>
                                </div>
                            </div>
                        </div>

                        <!-- Bundle Features -->
                        <div class="mb-4 mt-4">
                            <h3 class="text-base font-bold text-gray-900 mb-4">This bundle includes:</h3>
                            <ul class="space-y-3">
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 mt-1 p-1">
                                        <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                                    </div>
                                    <div class="ml-1">
                                        <span class="text-xs font-semibold text-gray-900">Full access</span>
                                        <span class="text-xs text-gray-600"> to study materials from <span class="font-semibold">IT Cert Prep</span>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 mt-1 p-1">
                                        <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                                    </div>
                                    <div class="ml-1">
                                        <span class="text-xs font-semibold text-gray-900">A diverse question bank,</span>
                                        <span class="text-xs text-gray-600"> featuring carefully selected questions compiled from multiple reputable sources</span>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 mt-1 p-1">
                                        <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                                    </div>
                                    <div class="ml-1">
                                        <span class="text-xs font-semibold text-gray-900">Learner-started personalized design,</span>
                                        <span class="text-xs text-gray-600"> tailored to each individual's learning journey</span>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 mt-1 p-1">
                                        <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                                    </div>
                                    <div class="ml-1">
                                        <span class="text-xs font-semibold text-gray-900">Exam pass rate indicator</span>
                                        <span class="text-xs text-gray-600"> — estimates your chance of passing the certification.</span>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 mt-1 p-1">
                                        <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                                    </div>
                                    <div class="ml-1">
                                        <span class="text-xs font-semibold text-gray-900">Advanced practice mode</span>
                                        <span class="text-xs text-gray-600"> for deep skill reinforcement and targeted improvement.</span>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 mt-1 p-1">
                                        <img src="<?= base_url() ?>/images/itcertprep/blue_check.svg" alt="check">
                                    </div>
                                    <div class="ml-1">
                                        <span class="text-xs font-semibold text-gray-900">Domain-based performance analytics,</span>
                                        <span class="text-xs text-gray-600"> helping you assess and track your proficiency in each knowledge area.</span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Step 2 -->

        <!-- Step 3 -->
        <div class="step-3 hidden step-content">
            <div class="flex flex-col items-center">
                <div class="w-full max-w-[558px] border border-gray-200 rounded-2xl p-6 md:p-10 flex flex-col items-center gap-4">
                    <!-- Success Illustration -->
                    <div class="w-full flex justify-center mb-8">
                        <img src="<?= base_url() ?>images/itcertprep/success_step_3.png" alt="Success" class="">
                    </div>

                    <!-- Content -->
                    <div class="w-full border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Thanks, <span id="user-name-display"><?= $user ?></span>. We've recorded your registration information.</h3>
                        <div class="flex flex-col gap-2">
                            <p class="text-sm font-medium text-gray-900">Just follow the steps below to activate your account.</p>
                            <p class="text-sm font-medium text-gray-900"><span class="font-bold">Step 1:</span> Double-check your selected certificate bundle.</p>
                            <p class="text-sm font-medium text-gray-900"><span class="font-bold">Step 2:</span> Tap <span class="font-bold">'Buy now'</span> to complete your purchase. Activation and email confirmation will be completed within 15 minutes after your payment.</p>
                        </div>
                    </div>

                    <!-- Certificate Info -->
                    <div class="w-full flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <div id="cert-icon" class="w-10 h-10 bg-cover bg-center rounded" style="background-image: url('<?= base_url() ?>upload/certificates/default.png');"></div>
                            <span id="cert-name" class="text-sm font-medium text-gray-900">PSD I Bundle - 12 Months</span>
                        </div>
                        <span id="cert-price" class="text-sm font-medium text-gray-900">$960</span>
                    </div>

                    <!-- Buy Now Button -->
                    <a href="https://sandbox-api.polar.sh/v1/checkout-links/polar_cl_GPE7e1SCeacYQdbiKOMUhCGnRAoqsNg0Jhw2E1iQrhL/redirect" data-polar-checkout data-polar-checkout-theme="dark" id="buy-now-btn" class="w-full bg-primary-blue-1 text-white py-3 px-4 rounded-lg font-medium mt-4 text-center">Buy now</a>
                </div>
            </div>
        </div>
        <!-- End Step 3 -->
        <!-- End Main Content -->
    </div>
    <!-- End Mobile Progress bar -->
    <!-- Step 4 -->
    <div class="step-4 w-screen h-screen hidden final-step-bg main-final-step-content">
        <div class="text-center">
            <div class="p-4">
                <h2 class="text-xl font-bold text-gray-900 mb-2">You're all set!</h2>
                <div class="step-4-content">
                    <p class="text-gray-500 text-sm font-medium">
                        Your registration for the PSD I Bundle - 12 months is complete.
                    </p>
                    <p class="text-gray-500 text-sm font-medium">
                        This should take no more than 15 minutes.
                    </p>
                    <p class="text-gray-500 text-sm font-medium">
                        Can't find the email? Check your Spam/Promotions folder or contact <NAME_EMAIL>
                    </p>
                </div>


            </div>
            <!-- Image -->
            <div class="lg:mb-12 mb-10 logo-last-step">
                <img src="<?= base_url() ?>/images/new/relax_signup.png" alt="Success Illustration" class="w-full hidden lg:block" />
                <img src="<?= base_url() ?>/images/new/relax_mobile.png" alt="Success Illustration" class="w-full center-block lg:hidden" />
            </div>

            <p class="text-sm font-bold text-gray-900 p-4 step-4-invite">
                Mời bạn tham gia cộng đồng học tập của ScrumPass để nhận các thông tin mới nhất về kỳ thi và hướng dẫn, tip trick ôn thi hiệu quả.
            </p>

            <!-- Buttons -->
            <div class="flex flex-col lg:flex-row items-center justify-center gap-3 lg:gap-4 mb-6 lg:mb-0 step-4-buttons">
                <a href="https://www.facebook.com/share/g/19wktQKRGC/" target="_blank" class="px-5 py-3 h-12 w-[220px] text-base font-medium bg-primary-blue-1 text-white rounded-lg hover:bg-blue-400 transition-colors text-center" id="join-group-btn">
                    Tham gia nhóm học tập
                </a>
                <a href="javascript:window.history.back()" class="px-5 py-3 h-12 w-[220px] text-base font-medium bg-primary-blue-1 text-white rounded-lg hover:bg-blue-400 transition-colors text-center step-4-back hidden" id="join-group-btn">
                    Quay lại trang học tập
                </a>
                <a href="https://scrumpass.com" target="_blank" class="px-5 py-3 lg:w-[145px] w-[220px] h-12 border border-primary-blue-1 text-primary-blue-1 rounded-lg hover:bg-blue-100 transition-colors text-base font-medium text-center" id="go-to-homepage-btn">
                    Về Trang chủ
                </a>
            </div>
        </div>
    </div>
    <!-- End Step 4 -->
    <!-- Footer -->
    <div class="footer hidden lg:block lg:fixed bottom-0 left-0 w-full border-t border-gray-200 z-10 bg-white">
        <div class="container ml-auto mr-auto p-4 flex justify-between">
            <div class="empty-box"></div>
            <div class="flex flex-col selected-box hidden">
                <span class="text-sm font-medium text-gray-500"><span class="selected-name"></span></span>
                <span class="selected-price text-sm font-bold text-gray-900"></span>
            </div>
            <div class="flex gap-4">
                <div class="back-btn hidden cursor-pointer border border-gray-200 rounded-lg py-3 px-5 text-gray-800 text-base font-medium h-12 w-fit">Back</div>
                <div class="next-btn cursor-pointer bg-gray-300 rounded-lg py-3 px-5 text-white text-base font-medium h-12 w-fit">Continue</div>
            </div>
        </div>
    </div>
    <!-- End Footer -->
    <!-- Mobile Footer -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t p-4 hidden lg:hidden mobile-footer">
        <div class="flex justify-between gap-4">
            <button class="back-btn px-6 py-2 border border-gray-200 rounded-lg text-gray-800 hover:bg-gray-50 flex-1">
                Quay lại
            </button>
            <button class="next-btn px-6 py-3 bg-primary-blue-1 text-white rounded-lg hover:bg-blue-700 flex-1">
                Tiếp tục
            </button>
        </div>
    </div>
</body>
<script>
    var notificationTimeout;

    function closeNotification() {
        $("#error_message").addClass("showNoti");
        $("#success_message").addClass("showNoti");
        if (notificationTimeout) {
            clearTimeout(notificationTimeout);
        }
    }

    function sendReportNotification(type, title) {
        closeNotification();
        if (type == "success") {
            $("#success_text").html(title);
            $("#success_message").removeClass("showNoti");
        } else {
            $("#error_text").html(title);
            $("#error_message").removeClass("showNoti");
        }
        notificationTimeout = setTimeout(function() {
            closeNotification();
        }, 3000);
    }

    $(document).ready(function() {
        $('input[datepicker]').on('focus', function(e) {
            e.preventDefault();
        });

        $('input[datepicker]').on('click', function() {
            $(this).datepicker('show');
        });
    });

    document.addEventListener("DOMContentLoaded", function() {
        var swiper = new Swiper(".certSwiper", {
            slidesPerView: 3,
            grid: {
                rows: 3,
                fill: "row",
            },
            spaceBetween: 8,
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
            // loop: true,
            slidesPerGroup: 3,
        });
    });

    function maxLengthCheck(element, maxLength) {
        if (element.value.length > maxLength) {
            element.value = element.value.slice(0, maxLength);
        }
    }

    function downloadQR(imageId, filename) {
        var image = document.getElementById(imageId).src;
        var a = document.createElement('a');
        a.href = image;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    let currentStep = 1;
    $(document).ready(function() {
        var certificates = JSON.parse('<?php echo $categories_json; ?>');
        var certId = 0;
        var certCategory = 0;
        var query = '';
        var selectedCert;
        var price = 0;
        var bankContent = '';
        var selectedPayment = 'immediate';
        var uid;
        var selectedBank = 'techcombank';
        var plan;
        var selectedPremium = false;
        var disabledCerts = [];
        var isUpgrade = false;
        var isAddMore = false;
        var selectedTitle = '';
        selectedPremium = localStorage.getItem('selectedPlan') === '12months_option';
        updateButtonBuyLink();
        checkIsUpgrade();
        checkIsAddMoreCert();
        renderList(certificates);
        renderSlide(certificates);

        function renderSlide(certificates) {
            var listContent = '';
            for (let index = 0; index < certificates.length; index++) {
                const element = certificates[index];
                const isDisabled = disabledCerts.includes(element.cid);
                listContent += `<div class="swiper-slide border border-gray-200 rounded-lg p-2 w-full cert-item transition-colors ${isDisabled ? 'disabled-cert' : 'cursor-pointer'} relative" certId="${element.cid}" litePrice="${element.lite_price}" fullPrice="${element.full_price}" hasFull="${element.has_full}">
                    <div class="flex flex-col items-center justify-center h-full">
                        <img src="<?= base_url() ?>upload/certificates/${element.icon}" alt="${element.category_name}" class="h-[76px] lg:h-[82px]">
                        <div class="gray-overlay"></div>
                        <span class="text-sm font-medium text-gray-900 text-center">${element.category_name}</span>
                    </div>
                </div>`;
            }
            if (certificates.length == 0) {
                $('.empty-list').removeClass('hidden');
            } else {
                $('.empty-list').addClass('hidden');
            }
            if (query != '') {
                $('.number-category').html(`Found <span class="font-bold text-gray-900">${certificates.length}</span> certificates with keyword <span class="font-bold text-gray-900">"${query}"</span>`);
            } else {
                $('.number-category').text(`${certificates.length} certificates`);
            }
            $('.certificate-list-mobile').html(listContent);
        }

        function renderList(certificates) {
            var listContent = '';
            const selectedCertId = localStorage.getItem('selectedCertId'); // Lấy selectedCertId từ localStorage

            for (let index = 0; index < certificates.length; index++) {
                const element = certificates[index];
                const isDisabled = disabledCerts.includes(element.cid);
                const isSelected = selectedCertId == element.cid ? 'selected' : ''; // Kiểm tra nếu chứng chỉ được chọn

                listContent += `<div class="border border-gray-200 rounded-lg p-2 w-full cert-item transition-colors ${isDisabled ? 'disabled-cert' : 'cursor-pointer'} relative ${isSelected}" certId="${element.cid}" litePrice="${element.lite_price}" fullPrice="${element.full_price}" hasFull="${element.has_full}">
                    <div class="flex flex-col items-center justify-center h-full">
                        <img src="<?= base_url() ?>upload/certificates/${element.icon}" alt="${element.category_name}" class="h-[76px] lg:h-[82px]">
                        <div class="gray-overlay"></div>
                        <span class="text-sm font-medium text-gray-900 text-center">${element.category_name}</span>
                    </div>
                </div>`;
            }

            if (certificates.length === 0) {
                $('.empty-list').removeClass('hidden');
            } else {
                $('.empty-list').addClass('hidden');
            }

            $('.number-category').text(`${certificates.length} certificates`);
            $('.certificate-list').html(listContent);
        }

        // Define the removeDiacritics function outside of nextStep
        function removeDiacritics(str) {
            const diacriticsMap = {
                'à': 'a',
                'á': 'a',
                'ả': 'a',
                'ã': 'a',
                'ạ': 'a',
                'ă': 'a',
                'ằ': 'a',
                'ắ': 'a',
                'ẳ': 'a',
                'ẵ': 'a',
                'ặ': 'a',
                'â': 'a',
                'ầ': 'a',
                'ấ': 'a',
                'ẩ': 'a',
                'ẫ': 'a',
                'ậ': 'a',
                'è': 'e',
                'é': 'e',
                'ẻ': 'e',
                'ẽ': 'e',
                'ẹ': 'e',
                'ê': 'e',
                'ề': 'e',
                'ế': 'e',
                'ể': 'e',
                'ễ': 'e',
                'ệ': 'e',
                'ì': 'i',
                'í': 'i',
                'ỉ': 'i',
                'ĩ': 'i',
                'ị': 'i',
                'ò': 'o',
                'ó': 'o',
                'ỏ': 'o',
                'õ': 'o',
                'ọ': 'o',
                'ô': 'o',
                'ồ': 'o',
                'ố': 'o',
                'ổ': 'o',
                'ỗ': 'o',
                'ộ': 'o',
                'ơ': 'o',
                'ờ': 'o',
                'ớ': 'o',
                'ở': 'o',
                'ỡ': 'o',
                'ợ': 'o',
                'ù': 'u',
                'ú': 'u',
                'ủ': 'u',
                'ũ': 'u',
                'ụ': 'u',
                'ư': 'u',
                'ừ': 'u',
                'ứ': 'u',
                'ử': 'u',
                'ữ': 'u',
                'ự': 'u',
                'ỳ': 'y',
                'ý': 'y',
                'ỷ': 'y',
                'ỹ': 'y',
                'ỵ': 'y',
                'đ': 'd',
                'À': 'A',
                'Á': 'A',
                'Ả': 'A',
                'Ã': 'A',
                'Ạ': 'A',
                'Ă': 'A',
                'Ằ': 'A',
                'Ắ': 'A',
                'Ẳ': 'A',
                'Ẵ': 'A',
                'Ặ': 'A',
                'Â': 'A',
                'Ầ': 'A',
                'Ấ': 'A',
                'Ẩ': 'A',
                'Ẫ': 'A',
                'Ậ': 'A',
                'È': 'E',
                'É': 'E',
                'Ẻ': 'E',
                'Ẽ': 'E',
                'Ẹ': 'E',
                'Ê': 'E',
                'Ề': 'E',
                'Ế': 'E',
                'Ể': 'E',
                'Ễ': 'E',
                'Ệ': 'E',
                'Ì': 'I',
                'Í': 'I',
                'Ỉ': 'I',
                'Ĩ': 'I',
                'Ị': 'I',
                'Ò': 'O',
                'Ó': 'O',
                'Ỏ': 'O',
                'Õ': 'O',
                'Ọ': 'O',
                'Ô': 'O',
                'Ồ': 'O',
                'Ố': 'O',
                'Ổ': 'O',
                'Ỗ': 'O',
                'Ộ': 'O',
                'Ơ': 'O',
                'Ờ': 'O',
                'Ớ': 'O',
                'Ở': 'O',
                'Ỡ': 'O',
                'Ợ': 'O',
                'Ù': 'U',
                'Ú': 'U',
                'Ủ': 'U',
                'Ũ': 'U',
                'Ụ': 'U',
                'Ư': 'U',
                'Ừ': 'U',
                'Ứ': 'U',
                'Ử': 'U',
                'Ữ': 'U',
                'Ự': 'U',
                'Ỳ': 'Y',
                'Ý': 'Y',
                'Ỷ': 'Y',
                'Ỹ': 'Y',
                'Ỵ': 'Y',
                'Đ': 'D'
            };

            return str.replace(/[àáảãạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵđÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÈÉẺẼẸÊỀẾỂỄỆÌÍỈĨỊÒÓỎÕỌÔỒỐỔỖỘƠỜỚỞỠỢÙÚỦŨỤƯỪỨỬỮỰỲÝỶỸỴĐ]/g, function(match) {
                return diacriticsMap[match] || match;
            });
        }

        function keepOnlyLetters(text) {
            return text.replace(/-/g, " ").replace(/[^a-zA-Z\s]/g, "").trim();
        }

        async function nextStep() {
            let storedStep = parseInt(localStorage.getItem('currentStep')) || 1;
            currentStep = storedStep; // Luôn đồng bộ với localStorage

            if (selectedCert == null) return;

            if (currentStep == 1) {
                window.scrollTo(0, 0);
                changeStep();
                return;
            }

            if (currentStep == 2) {
                HoldOn.open({
                    theme: "sk-cube-grid"
                });

                validateName();
                validateSource();
                validateTerms();
                let isEmailValid = await validateEmailField(); // Chờ kết quả email

                var isValid = $('input.has-error').length === 0 && $('select.has-error').length === 0 && isEmailValid;

                if (!isValid) {
                    HoldOn.close();
                    return;
                }

                localStorage.setItem('user_email', $('input[name="email"]').val());

                const storedData = JSON.parse(localStorage.getItem('formData')) || {};

                bankContent = `${removeDiacritics(storedData.name || '')}
                    ${storedData.contact_no || ''}
                    ${keepOnlyLetters(selectedCert.category_name || '')}
                    ${selectedPremium ? 'Premium' : 'Lite'}`;

                $('.bank-content').text(bankContent);

                HoldOn.close();
                changeStep();
                return;
            }

            if (currentStep == 3) {
                if (isUpgrade) {
                    upgradeAccount();
                    return;
                }
                if (isAddMore) {
                    addMoreCert();
                    return;
                }
                createAccount();
                return;
            }
        }

        $(document).ready(async function() {
            let storedStep = parseInt(localStorage.getItem('currentStep')) || 1;

            currentStep = storedStep;

            if (currentStep == 2) {
                HoldOn.open({
                    theme: "sk-cube-grid"
                });

                validateName();
                validatePhone();
                validateSource();
                let isEmailValid = await validateEmailField();

                var isValid = $('input.has-error').length === 0 && $('select.has-error').length === 0 && isEmailValid;

                HoldOn.close();
            }
        });

        function changeStep() {
            if (isAddMore && currentStep == 1) {
                currentStep = 3;
            } else {
                currentStep++;
            }
            localStorage.setItem('currentStep', currentStep);

            $('.step-content').hide();
            $(`.step-${currentStep}`).fadeIn();

            if (currentStep == 2) {
                $('input[name="full_name"]').focus();
                $('.back-btn').removeClass('hidden');
            } else if (currentStep == 3) {
                $('.footer').fadeOut();
            }

            if ($('.progress-2').hasClass('current-step') && currentStep == 2) {
                // Do nothing
            } else {
                $('.current-step').addClass('step-done').removeClass('current-step');
            }

            if (currentStep == 2) {
                $('.progress-1 img').addClass('hidden');
                $('.progress-1 img.step-done').removeClass('hidden');
                $('.progress-2').addClass('current-step');
                $('.progress-2 img').addClass('hidden');
                $('.progress-2 img.step-current').removeClass('hidden');
                $('.mobile-footer').removeClass('hidden');
            }

            if (currentStep == 3) {
                $('.progress-2 img').addClass('hidden');
                $('.progress-2 img.step-done').removeClass('hidden');
                $('.progress-3').addClass('current-step');
                $('.progress-3 img').addClass('hidden');
                $('.progress-3 img.step-current').removeClass('hidden');
                $('.mobile-footer').addClass('hidden');

                // Update Step 3 content with user data
                const formData = JSON.parse(localStorage.getItem('formData')) || {};
                const userName = formData.name || 'User';
                $('#user-name-display').text(userName);

                // Update certificate information
                if (selectedCert) {
                    selectedTitle = `${selectedCert.category_name} Bundle - ${selectedPremium ? '12 Months' : '1 Month'}`;
                    localStorage.setItem('certTitle', selectedTitle);
                    $('#cert-icon').css('background-image', `url('<?= base_url() ?>upload/certificates/${selectedCert.icon}')`);
                    $('#cert-name').text(`${selectedCert.category_name} Bundle - ${selectedPremium ? '12 Months' : '1 Month'}`);
                    $('#cert-price').text(`$${selectedPremium ? '99.99' : '12.99'}`);
                }
            }

            if (currentStep == 4) {
                $('.progress-bar').hide();
                $('.main-content').hide();
                if (isUpgrade == false && isAddMore == false) {
                    checkFirstLogin();
                }
            }
        }

        function createAccount() {
            let formData = JSON.parse(localStorage.getItem('formData')) || {};

            const selectedPlan = localStorage.getItem('selectedPlan');
            const plan = selectedPlan === '12months_option' ? 1 : 0; // ✅ Trả về 1 nếu 12months_option, 0 nếu 1month_option

            let selectedCertId = localStorage.getItem('selectedCertId') || '';
            let certId = selectedCertId;

            const selectedPayment = localStorage.getItem('selectedPayment') || '';
            const paymentStatus = selectedPayment === 'immediate' ? 1 : 0;

            HoldOn.open({
                theme: "sk-cube-grid"
            });

            $.ajax({
                type: 'POST',
                url: "<?php echo site_url('apiSignUp/add_account'); ?>",
                data: {
                    ...formData,
                    product_packet: plan, // ✅ Gửi 1 (Premium) hoặc 0 (Lite)
                    category_id: certId,
                    payment_status: paymentStatus,
                },
                cache: false,
                dataType: 'json',
                success: function(output) {
                    if (output.error === false) {
                        uid = output.data;
                        localStorage.setItem('uid', uid);
                        updatePayment(); // Gọi API thanh toán ngay sau khi tạo tài khoản
                    } else {
                        HoldOn.close();
                        toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                    }
                },
                error: function() {
                    HoldOn.close();
                    toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                }
            });
        }

        function updatePayment() {
            // Lấy giá trị từ localStorage
            const selectedBank = localStorage.getItem('selectedBank') || '';
            const selectedPayment = localStorage.getItem('selectedPayment') || '';

            const paymentStatus = selectedPayment === 'immediate' ? 1 : 0;
            $.ajax({
                type: 'POST',
                url: "<?php echo site_url('apiSignUp/update_payment_status'); ?>",
                data: {
                    uid: uid,
                    payment_status: paymentStatus,
                    payment_method: selectedBank,
                },
                cache: false,
                dataType: 'json',
                success: function(output) {
                    if (output.error == false) {
                        changeStep();
                    } else {
                        toasterMessage('error', 'Có lỗi xảy ra khi cập nhật thanh toán.');
                    }
                    HoldOn.close();
                },
                error: function() {
                    HoldOn.close();
                    toasterMessage('error', 'Lỗi hệ thống, vui lòng thử lại.');
                }
            });
        }

        async function checkEmailDuplicate() {
            const formData = JSON.parse(localStorage.getItem('formData')) || {};
            var emailValue = formData.email ? formData.email.trim() : '';

            return new Promise((resolve, reject) => {
                $.ajax({
                    type: 'POST',
                    url: "<?php echo site_url('apiSignUp/check_email_duplicate'); ?>",
                    data: {
                        email: emailValue
                    },
                    cache: false,
                    dataType: 'json',
                    success: function(output) {
                        var element = $('input[name="email"]');

                        if (output.error) {
                            element.addClass('has-error');
                            element.next('.error-message').text('Email is existing.');
                            resolve(false); // Trả về false nếu email trùng
                        } else {
                            element.removeClass('has-error');
                            element.next('.error-message').text('');
                            resolve(true); // Trả về true nếu email hợp lệ
                        }
                    },
                    error: function() {
                        toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                        resolve(false);
                    }
                });
            });
        }

        function backStep() {
            currentStep--;
            localStorage.setItem('currentStep', currentStep); // Cập nhật lại step hiện tại

            $('.step-content').hide();
            $(`.step-${currentStep}`).fadeIn();

            if (currentStep == 1) {
                $('.back-btn').addClass('hidden');
            }
            $('.mobile-footer').addClass('hidden');
        }


        function validateName() {
            // Lấy dữ liệu từ localStorage
            const formData = JSON.parse(localStorage.getItem('formData')) || {};
            var nameValue = formData.name ? formData.name.trim() : '';

            var element = $('input[name="full_name"]');
            var regex = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂạảấầẩẫậắằẳẵặẹẻẽềếểễệỉịọỏốồổỗộớờởỡợụủứừửữự\s]+$/;

            if (nameValue === '') {
                element.addClass('has-error');
                element.next('.error-message').text('Please enter your full name.');
            } else if (!regex.test(nameValue)) {
                element.addClass('has-error');
                element.next('.error-message').text('Name can only contain letters.');
            } else {
                element.removeClass('has-error');
                element.next('.error-message').text('');
            }
        }

        async function validateEmailField() {
            const formData = JSON.parse(localStorage.getItem('formData')) || {};
            var emailValue = formData.email ? formData.email.trim() : '';

            var element = $('input[name="email"]');

            if (emailValue === '') {
                element.addClass('has-error');
                element.next('.error-message').text('Please enter your email.');
                return false;
            } else if (!validateEmail(emailValue)) {
                element.addClass('has-error');
                element.next('.error-message').text('Email is not in the correct format.');
                return false;
            } else {
                return await checkEmailDuplicate(); // Đợi AJAX trả về kết quả
            }
        }


        function validateSource() {
            // Lấy dữ liệu từ localStorage
            const formData = JSON.parse(localStorage.getItem('formData')) || {};
            var sourceValue = formData.know_from ? formData.know_from.trim() : '';

            var element = $('select[name="source"]');

            if (sourceValue === '' || sourceValue == null) {
                element.addClass('has-error');
                element.next('.error-message').text('Please select how you know.');
            } else {
                element.removeClass('has-error');
                element.next('.error-message').text('');
            }
        }

        function validateTerms() {
            var isChecked = $('#terms_agreement').is(':checked');
            var element = $('#terms_agreement');

            if (isChecked == false) {
                element.addClass('has-error');
                $('.term-error-message').text('Please accept the terms and conditions.');
            } else {
                element.removeClass('has-error');
                $('.term-error-message').text('');
            }
        }

        function updateButtonBuyLink() {
            if (selectedPremium) {
                $('#buy-now-btn').attr('href', 'https://sandbox-api.polar.sh/v1/checkout-links/polar_cl_jfOPCnC2d8eEtRzUJNk6u962VH05Z1laarGNN17BvSS/redirect');
            } else {
                $('#buy-now-btn').attr('href', 'https://sandbox-api.polar.sh/v1/checkout-links/polar_cl_GPE7e1SCeacYQdbiKOMUhCGnRAoqsNg0Jhw2E1iQrhL/redirect');
            }
        }

        $('#dropdownRadio input').on('change', function() {
            var selected = $('input[name=filter-radio]:checked', '#dropdownRadio').attr('title');
            var value = $('input[name=filter-radio]:checked', '#dropdownRadio').val();
            certCategory = value;

            // Lưu lại cert category vào localStorage
            localStorage.setItem('selectedCertCategory', value);

            if (value == 0) {
                $('#current_type').text('Tất cả');
                renderList(certificates);
                renderSlide(certificates);
            } else {
                $('#current_type').text(selected);
                const filteredCertificates = certificates.filter(cert => cert.category_id == value && cert.category_name.toLowerCase().includes(query));
                renderList(filteredCertificates);
                renderSlide(filteredCertificates);
            }

            $('#dropdownRadioButton').click();
        });
        $(document).ready(function() {
            const selectedCertCategory = localStorage.getItem('selectedCertCategory');

            if (selectedCertCategory) {
                const targetRadio = $(`input[name="filter-radio"][value="${selectedCertCategory}"]`);
                $('.next-btn').addClass('active');
                if (targetRadio.length > 0) {
                    targetRadio.prop('checked', true).trigger('change'); // Auto chọn lại & kích hoạt lọc
                }
            }
        });

        $('.next-btn').click(function(e) {
            e.preventDefault();
            logEvent('click_btn_next', false);
            nextStep();
        });
        $('.back-btn').click(function(e) {
            e.preventDefault();
            backStep();
        });
        $('.finish-btn').click(function(e) {
            e.preventDefault();
            logEvent('click_btn_complete', false);
            nextStep();
        });

        $('#buy-now-btn').click(function(e) {
            // e.preventDefault();
            logEvent('click_btn_buy_now', false);
        });

        $('.logo').click(function(e) {
            logEvent('logo_click', false);
        });
        // $('input[name="full_name"]').on('blur', function() {
        //     validateName();
        // });
        // $('input[name="email"]').on('blur', function() {
        //     validateEmailField();
        // });
        // $('input[name="phone_number"]').on('blur', function() {
        //     validatePhone();
        // });
        // $('select[name="source"]').on('change', function() {
        //     validateSource();
        // });

        $(document).on('click', '.cert-item', function() {
            if ($(this).hasClass('disabled-cert')) return;
            $('.cert-item').removeClass('selected');
            $(this).addClass('selected');

            certId = $(this).attr('certId');
            localStorage.setItem('selectedCertId', certId);
            updateUI();
            logEvent('select_certificate', true);
        });

        $('input[name="exam-date"]').on('changeDate', function() {
            $(this).trigger('change');
        });

        // $('input, select').on('input change', function() {
        //     const formData = {
        //         name: $('input[name="full_name"]').val(),
        //         email: $('input[name="email"]').val(),
        //         know_from: $('select[name="source"]').val(),
        //         exam_date: $('input[name="exam-date"]').val(),
        //     };

        //     localStorage.setItem('formData', JSON.stringify(formData));
        // });
        $('input, select').on('focus', function() {
            localStorage.setItem('focusedInput', $(this).attr('name'));
        });
        $(document).ready(function() {
            // Lấy dữ liệu formData từ localStorage khi trang tải lại
            const savedFormData = JSON.parse(localStorage.getItem('formData')) || {};

            // Danh sách key cần lưu trữ (khớp với API và sự kiện change)
            const formFields = {
                full_name: 'name',
                email: 'email',
                phone_number: 'contact_no',
                social_account: 'social_media',
                company: 'company',
                job_title: 'job_title',
                source: 'know_from',
                referrer_info: 'introducer',
                "exam-date": 'exam_date',
                shipping_address: 'address'
            };

            // Đặt lại giá trị cho các input từ localStorage
            Object.entries(formFields).forEach(([inputName, apiKey]) => {
                if (savedFormData[apiKey]) {
                    $(`[name="${inputName}"]`).val(savedFormData[apiKey]);
                }
            });

            // Chỉ lưu dữ liệu của các input vào localStorage khi người dùng nhập
            $('input, select').on('input change', function() {
                const formData = {};
                Object.entries(formFields).forEach(([inputName, apiKey]) => {
                    formData[apiKey] = $(`[name="${inputName}"]`).val();
                });

                localStorage.setItem('formData', JSON.stringify(formData));
            });

            // Lấy input được focus trước đó và kiểm tra xem nó có trong danh sách formFields không
            const savedFocusedInput = localStorage.getItem('focusedInput');
            if (savedFocusedInput && formFields[savedFocusedInput]) {
                $(`[name="${savedFocusedInput}"]`).focus();
            }

            // Chỉ lưu lại input được focus nếu nó nằm trong danh sách formFields
            $('input, select').on('focus', function() {
                const inputName = $(this).attr('name');
                if (formFields[inputName]) {
                    localStorage.setItem('focusedInput', inputName);
                }
            });
        });

        const savedStep = localStorage.getItem('currentStep');
        if (savedStep) {
            currentStep = parseInt(savedStep);
        } else {
            currentStep = 1;
        }

        console.log('currentStep', currentStep);

        const savedCertId = localStorage.getItem('selectedCertId');
        if (savedCertId) {
            $(`.cert-item[certId="${savedCertId}"]`).addClass('selected');
            certId = savedCertId;
            updateUI();
        }

        const savedPlan = localStorage.getItem('selectedPlan');
        if (savedPlan) {
            $(`#${savedPlan}`).prop('checked', true).trigger('change');
        }

        const savedBank = localStorage.getItem('selectedBank');
        if (savedBank) {
            $('.bank-option').removeClass('selected');
            $(`.bank-option[value="${savedBank}"]`).addClass('selected');

            $('.bank-content-wrapper').addClass('hidden');
            $('#' + savedBank).removeClass('hidden');
            $('#' + savedBank + "-detail").removeClass('hidden');

            selectedBank = savedBank;
        } else {
            selectedBank = 'techcombank';
            localStorage.setItem('selectedBank', 'techcombank');
        }

        const savedPayment = localStorage.getItem('selectedPayment');
        if (savedPayment) {
            selectedPayment = savedPayment;
            $(`input[name="payment"][value="${savedPayment}"]`).prop('checked', true);

            $('.payment-type-message').addClass('hidden');
            if (savedPayment == 'immediate') {
                $('.payment-type').text('thanh toán ngay');
                $('#type-immediate').removeClass('hidden');
            } else {
                $('.payment-type').text('thanh toán sau');
                $('#type-later').removeClass('hidden');
            }
        } else {
            selectedPayment = 'immediate';
            localStorage.setItem('selectedPayment', selectedPayment);
        }

        $('.step-content').hide();
        $(`.step-${currentStep}`).fadeIn();

        if (currentStep == 2) {
            const savedFocusedInput = localStorage.getItem('focusedInput');
            if (savedFocusedInput) {
                $(`input[name="${savedFocusedInput}"], select[name="${savedFocusedInput}"]`).focus();
            }
            $('.back-btn').removeClass('hidden');
        } else if (currentStep == 3 || currentStep == 4) {
            $('.footer').hide();
        }

        if ($('.progress-2').hasClass('current-step') && currentStep == 2) {
            // Do nothing
        } else if (currentStep != 1) {
            $('.current-step').addClass('step-done').removeClass('current-step');
        }

        if (currentStep == 2) {
            $('.progress-1 img').addClass('hidden');
            $('.progress-1 img.step-done').removeClass('hidden');
            $('.progress-2').addClass('current-step');
            $('.progress-2 img').addClass('hidden');
            $('.progress-2 img.step-current').removeClass('hidden');
            $('.mobile-footer').removeClass('hidden');
        }

        if (currentStep == 3) {
            $('.progress-1 img').addClass('hidden');
            $('.progress-1 img.step-done').removeClass('hidden');
            $('.progress-2').addClass('step-done');
            $('.progress-2 img').addClass('hidden');
            $('.progress-2 img.step-done').removeClass('hidden');
            $('.progress-3').addClass('current-step');
            $('.progress-3 img').addClass('hidden');
            $('.progress-3 img.step-current').removeClass('hidden');
            $('.mobile-footer').addClass('hidden');
        }

        if (currentStep == 4) {
            $('.progress-bar').hide();
            $('.main-content').hide();
            checkFirstLogin();
        }

        $('input[name="plan"]').on('change', function() {
            if ($(this).attr('id') === '1month_option') {
                selectedTitle = `${selectedCert.category_name} Bundle - 1 Month`;
                $('.selected-name').text(`${selectedCert.category_name} Bundle - 1 Month`);
                $('.selected-price').text(`$12,99`);
                price = 12.99;
                selectedPremium = false;
                plan = 'lite';
            } else {
                selectedTitle = `${selectedCert.category_name} Bundle - 12 Months`;
                $('.selected-name').text(`${selectedCert.category_name} Bundle - 12 Months`);
                $('.selected-price').text(`$99,99`);
                price = 99.99;
                selectedPremium = true;
                plan = 'full';
            }
            const selectedPlan = $(this).attr('id');
            localStorage.setItem('selectedPlan', selectedPlan);
            updateButtonBuyLink();
            updatePrice();
        });

        $('body').click(function(e) {
            if ($('#dropdownRadio').hasClass('block')) {
                $('#dropdownRadioButton').addClass('active');
            } else {
                $('#dropdownRadioButton').removeClass('active');
            }
        });
        let debounceTimer;
        $('.search-box input').on('input', function() {
            query = $(this).val().toLowerCase();
            onSearch();
        });

        function onSearch() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                if (certCategory == 0) {
                    const filteredCertificates = certificates.filter(cert => cert.category_name.toLowerCase().includes(query));
                    renderList(filteredCertificates);
                    renderSlide(filteredCertificates);
                } else {
                    const filteredCertificates = certificates.filter(cert => cert.category_name.toLowerCase().includes(query) && cert.category_id == certCategory);
                    renderList(filteredCertificates);
                    renderSlide(filteredCertificates);
                }
            }, 300);
        }
        $('.copy-bank-account').click(function(e) {
            var textToCopy = $(this).val();
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(textToCopy).then(function() {
                    toasterMessage('success', 'Sao chép thành công!');
                }, function(err) {
                    console.error('Could not copy text: ', err);
                });
            } else {
                // Fallback method for copying text
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(textToCopy).select();
                document.execCommand('copy');
                tempInput.remove();
                toasterMessage('success', 'Sao chép thành công!');
            }
        });

        $('.copy-bank-content').click(function(e) {
            // Loại bỏ xuống dòng và thay thế bằng dấu cách
            var textToCopy = bankContent.replace(/\s+/g, ' ').trim();

            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(textToCopy).then(function() {
                    toasterMessage('success', 'Sao chép thành công!');
                }, function(err) {
                    console.error('Có lỗi xảy ra');
                });
            } else {
                // Fallback method for copying text
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(textToCopy).select();
                document.execCommand('copy');
                tempInput.remove();
                toasterMessage('success', 'Sao chép thành công!');
            }
        });

        $('input[name="payment"]').on('change', function(e) {
            selectedPayment = $(this).val();
            localStorage.setItem('selectedPayment', selectedPayment); // Lưu vào Local Storage

            $('.payment-type-message').addClass('hidden');
            if (selectedPayment == 'immediate') {
                $('.payment-type').text('thanh toán ngay');
                $('#type-immediate').removeClass('hidden');
            } else {
                $('.payment-type').text('thanh toán sau');
                $('#type-later').removeClass('hidden');
            }
            logEvent('select_payment_type', true);
        });

        $(document).on('click', '.bank-option', function() {
            $('.bank-option').removeClass('selected');
            $(this).addClass('selected');
            var bankId = $(this).attr('value');
            selectedBank = bankId;
            localStorage.setItem('selectedBank', bankId);

            $('.bank-content-wrapper').addClass('hidden');
            $('#' + bankId).removeClass('hidden');
            $('#' + bankId + "-detail").removeClass('hidden');
        });


        function formatNumber(num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.')
        }

        function updateUI() {
            // Find the selected certificate from the certificates array
            selectedCert = certificates.find(x => x.cid == certId);

            // Update UI elements
            $('.next-btn').addClass('active');
            $('.empty-box').addClass('hidden');
            $('#1month_option').removeAttr("disabled");
            $('#12months_option').removeAttr("disabled");

            $('.mobile-full-price').text(`$99,99`);
            $('.mobile-lite-price').text(`$12,99`);

            // if (currentStep == 1) {
                // if (selectedCert.has_full == 1 && (selectedCert.full_price != null && selectedCert.full_price != 0)) {
                //     plan = 'full';
                //     selectedPremium = true;
                // } else {
                //     plan = 'lite';
                //     selectedPremium = false;
                // }
                $('.selected-cert-name').text(selectedCert.category_name);
                if (selectedPremium) {
                    $('#12months_option').removeAttr("disabled").prop('checked', true);
                    $('#1month_option').prop('checked', false);
                    price = 99.99;
                    $('.selected-price').text(`$${formatNumber(price)}`);
                    $('.plan-price').text(`$${formatNumber(price)}`);
                    $('.plan-monthly-price').text(`$${formatNumber(8.33)}`);
                    $('.plan-duration').text(`12 Months`);
                } else {
                    $('#12months_option').prop('checked', false);
                    $('#1month_option').prop('checked', true);
                    price = 12.99;
                    $('.selected-price').text(`$${formatNumber(price)}`);
                    $('.plan-price').text(`$${formatNumber(price)}`);
                    $('.plan-monthly-price').text(`$${formatNumber(price)}`);
                    $('.plan-duration').text(`1 Month`);
                }
                updateButtonBuyLink();

                // Lưu lại plan vào localStorage để duy trì trạng thái
                localStorage.setItem('selectedPlan', selectedPremium ? '12months_option' : '1month_option');
            // }

            // Disable nếu không có giá Lite
            // if (selectedCert.lite_price == null || selectedCert.lite_price == 0) {
            //     $('#1month_option').prop('disabled', true);
            // } else {
            //     $('#1months_option').removeAttr("disabled");
            // }
            selectedTitle = `${selectedCert.category_name} Bundle - ${selectedPremium ? '12 Months' : '1 Month'}`;
            $('.selected-name').text(`${selectedCert.category_name} Bundle - ${selectedPremium ? '12 Months' : '1 Month'}`);
            $('.selected-box').removeClass('hidden');

            updatePrice();
        }

        function updatePrice(params) {
            // Lấy dữ liệu từ localStorage
            const storedData = JSON.parse(localStorage.getItem('formData')) || {};

            // Cập nhật bankContent từ dữ liệu đã lưu
            if (isUpgrade == false && isAddMore == false) {
                bankContent = `${removeDiacritics(storedData.name || 'Nguyen Van A')}
                ${storedData.contact_no || '0123 456 789'}
                ${keepOnlyLetters(selectedCert.category_name || '')}
                ${selectedPremium ? 'Premium' : 'Lite'}`;
            } else if (isAddMore) {
                bankContent = `${removeDiacritics('<?= $this->session->userdata("logged_in")["name"] ?>' || 'Nguyen Van A')}
                ${'<?= $this->session->userdata("logged_in")["contact_no"] ?>' || '0123 456 789'}
                ${keepOnlyLetters(selectedCert.category_name || '')}
                ${selectedPremium ? 'Premium' : 'Lite'}`;
            }

            $('.step-3-selected-cert').html(`
                <div class="flex items-center gap-1">
                    <img class="w-11 h-11" src="<?= base_url() ?>/upload/certificates/${selectedCert.icon}" alt="${selectedCert.category_name}">
                    <p>Gói ${selectedCert.category_name} - ${selectedPremium ? 'Premium' : 'Lite'}</p>
                </div>
                <div>
                    <p class="pl-1">${formatNumber(price)}đ</p>
                </div>
            `);

            $('.bank-price').text(`${formatNumber(price)}đ`);
            $('.bank-content').text(bankContent);
            $('.step-4-selected-cert').text(`${selectedCert.category_name} - gói ${selectedPremium ? 'Premium' : 'Lite'}`);
        }

        function validateEmail(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        function toasterMessage(type, message) {
            $('.notifyjs-corner').empty();
            if (type == 'success') {
                var title = '';
                var icon = BASE_URL + "assets/backend/img/alert-icons/alert-checked.svg";
            }
            if (type == 'info') {
                // var title = 'Info';
                var title = '';
                var icon = BASE_URL + "assets/backend/img/alert-icons/alert-checked.svg";
            }
            if (type == 'warning') {
                var title = '';
                var icon = BASE_URL + "assets/backend/img/alert-icons/alert-danger.svg";
            }
            if (type == 'error') {
                var title = '';
                var icon = BASE_URL + "assets/backend/img/alert-icons/alert-disabled.svg";
            }
            if (type == 'error')
                type = "danger";
            $.notify({
                //icon: icon,
                title: "<strong>" + title + "</strong> ",
                message: message
            }, {
                icon_type: 'image',
                type: type,
                allow_duplicates: false
            });
        }

        var certificateNames = certificates.map(function(cert) {
            return cert.category_name;
        });

        const autoCompleteJS = new autoComplete({
            placeHolder: "Looking for a specific certificate?",
            data: {
                src: certificateNames,
                cache: true,
            },
            resultItem: {
                highlight: true
            },
            debounce: 300,
            events: {
                input: {
                    selection: (event) => {
                        const selection = event.detail.selection.value;
                        autoCompleteJS.input.value = selection;
                        query = selection.toLowerCase();
                        onSearch();
                    }
                }
            }
        });

        $('.search-box').click(function(e) {
            logEvent('click_search', false);
        });
        $('#dropdownRadioButton').click(function(e) {
            logEvent('click_filter', false);
        });
        $('.plan-item').click(function(e) {
            logEvent('select_package', true);
        });
        $('#help-button').click(function(e) {
            logEvent('click_btn_support', false);
        });
        $('#go-to-homepage-btn').click(function(e) {
            logEvent('click_btn_secondary', false);
        });
        $('#join-group-btn').click(function(e) {
            logEvent('click_btn_primary', false);
        });

        function getLocalStorage(key) {
            return JSON.parse(localStorage.getItem(key));
        }

        function setLocalStorage(key, data) {
            localStorage.setItem(key, JSON.stringify(data));
        }

        function detectDeviceType() {
            // Method 1: Screen width and touch capabilities
            const width = window.innerWidth;
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

            // Mobile detection
            if (width <= 768 && isTouchDevice) {
                return 'mobile';
            }

            // Tablet detection
            if (width > 768 && width <= 1024 && isTouchDevice) {
                return 'tablet';
            }

            // Desktop detection
            if (width > 1024 || !isTouchDevice) {
                return 'desktop';
            }

            // Fallback
            return 'desktop';
        }

        function detectOperatingSystem() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();

            // Detailed OS detection
            const osDetection = {
                name: '',
                version: '',
                architecture: navigator.userAgent.includes('WOW64') || navigator.userAgent.includes('Win64') ? '64-bit' : '32-bit'
            };

            // Windows detection
            if (userAgent.includes('win')) {
                osDetection.name = 'Windows';
                if (userAgent.includes('windows nt 10.0')) osDetection.version = '10';
                else if (userAgent.includes('windows nt 6.3')) osDetection.version = '8.1';
                else if (userAgent.includes('windows nt 6.2')) osDetection.version = '8';
                else if (userAgent.includes('windows nt 6.1')) osDetection.version = '7';
                else if (userAgent.includes('windows nt 6.0')) osDetection.version = 'Vista';
                else if (userAgent.includes('windows nt 5.2')) osDetection.version = 'Server 2003';
                else if (userAgent.includes('windows nt 5.1')) osDetection.version = 'XP';
            }
            // macOS detection
            else if (userAgent.includes('mac')) {
                osDetection.name = 'macOS';
                const macMatch = userAgent.match(/mac os x ([\d_]+)/);
                if (macMatch) {
                    osDetection.version = macMatch[1].replace(/_/g, '.');
                }
            }
            // Linux detection
            else if (userAgent.includes('linux')) {
                osDetection.name = 'Linux';
                if (userAgent.includes('android')) {
                    osDetection.name = 'Android';
                    const androidMatch = userAgent.match(/android ([\d.]+)/);
                    if (androidMatch) {
                        osDetection.version = androidMatch[1];
                    }
                } else if (platform.includes('linux')) {
                    // Attempt to get more specific Linux distribution
                    const linuxDistros = ['ubuntu', 'fedora', 'debian', 'centos', 'rhel'];
                    for (let distro of linuxDistros) {
                        if (userAgent.includes(distro)) {
                            osDetection.version = distro;
                            break;
                        }
                    }
                }
            }
            // iOS detection
            else if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ipod')) {
                osDetection.name = 'iOS';
                const iOSMatch = userAgent.match(/os ([\d_]+)/);
                if (iOSMatch) {
                    osDetection.version = iOSMatch[1].replace(/_/g, '.');
                }
            }
            // Chrome OS detection
            else if (userAgent.includes('cros')) {
                osDetection.name = 'Chrome OS';
            }

            return osDetection;
        }

        function getParamFromScreen() {
            switch (currentStep) {
                case 1:
                    return 'pg_certificate_view';
                    break;
                case 2:
                    return 'pg_register_account';
                    break;
                case 3:
                    return 'pg_payment';
                    break;
                case 4:
                    return 'pg_completed';
                    break;
                default:
                    break;
            }
        }


        function logEvent(eventName, hasCustomize = false) {
            const currentTime = new Date().toLocaleString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });



            var userId = getLocalStorage('user_id');
            const deviceType = detectDeviceType();
            const deviceOS = detectOperatingSystem();
            const urlParams = new URLSearchParams(window.location.search);
            const source = urlParams.get('source') || "";

            if (userId == null) {
                userId = Math.random().toString(36).substring(2, 10) + (new Date()).getTime().toString(36);
                setLocalStorage('user_id', userId);
            }

            const selectedPayment = localStorage.getItem('selectedPayment') || '';

            const eventProperties = {
                "timestamp": currentTime,
                "user_id": userId,
                "device_id": userId,
                "device_type": deviceType,
                "device_platform": deviceOS,
                "screen_name": getParamFromScreen(),
                "previous_link": source,
                "certificate_name": hasCustomize ? selectedCert.category_name : "",
                "study_type": hasCustomize ? (selectedPremium ? "Premium" : "Lite") : '',
                "purchase_type": hasCustomize ? selectedPayment : ''
            };
            console.log(eventProperties);
            window.amplitude.logEvent(eventName, eventProperties);
        }

        function checkFirstLogin() {
            let uid = localStorage.getItem('uid');

            if (!uid) {
                toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                return;
            }

            $.ajax({
                type: 'GET',
                url: "<?php echo site_url('apiSignUp/getFirstLogin'); ?>",
                data: {
                    uid: uid
                },
                dataType: 'json',
                success: function(response) {
                    if (response.error === false && response.last_login_date != null) {
                        localStorage.removeItem('selectedBank');
                        localStorage.removeItem('focusedInput');
                        localStorage.removeItem('selectedCertId');
                        localStorage.removeItem('selectedPlan');
                        localStorage.removeItem('formData');
                        localStorage.removeItem('selectedPayment');
                        localStorage.removeItem('currentStep');
                        window.location.href = window.location.href;

                    }
                },
                error: function() {
                    toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');

                }
            });
        }

        function checkIsUpgrade() {
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type') || "";
            const userName = '<?= $this->session->userdata('logged_in')['name'] ?>';
            const phone = '<?= $this->session->userdata('logged_in')['contact_no'] ?>';
            if (type === 'upgrade') {
                isUpgrade = true;
                const upgradePlan = urlParams.get('plan') || "";
                if (upgradePlan === 'full') {
                    selectedPremium = true;
                    plan = 'full';
                }
                const cid = urlParams.get('cid') || "";
                if (cid) {
                    certId = cid;
                    localStorage.setItem('selectedCertId', certId);
                    updateUI();
                    currentStep = 2;
                    changeStep();
                    bankContent = `${removeDiacritics(userName || 'Nguyen Van A')} ${phone || '0123 456 789'} ${keepOnlyLetters(selectedCert.category_name || '')} ${selectedPremium ? 'Premium' : 'Lite'}`;
                }
            }
        }

        function upgradeAccount() {
            const urlParams = new URLSearchParams(window.location.search);
            const uid = urlParams.get('uid') || "";
            $.ajax({
                type: 'POST',
                url: "<?php echo site_url('apiSignUp/upgrade_account'); ?>",
                data: {
                    uid: uid,
                    product_packet: plan,
                    category_id: certId,
                    payment_status: selectedPayment,
                    payment_method: selectedBank,
                },
                cache: false,
                dataType: 'json',
                beforeSend: function() {
                    HoldOn.open({
                        theme: "sk-cube-grid"
                    });
                },
                success: function(output) {
                    if (output.error === false) {
                        HoldOn.close();
                        $('.step-4-invite').addClass('hidden');
                        $('.step-4-button a').toggleClass('hidden');
                        $('.step-4-content').html(
                            `<p class="text-gray-500 text-sm font-medium">
                        Bạn đã hoàn thành nâng cấp gói luyện thi chứng chỉ <span class="font-bold">${selectedCert.category_name} ${selectedPremium ? 'Premium' : 'Lite'}</span> với phương thức <span class="font-bold">${selectedPayment == 'immediate' ? 'thanh toán ngay' : 'thanh toán sau'}</span>. Tài khoản của bạn sẽ được cập nhật sau khi chúng tôi xác nhận thông tin thanh toán của bạn trong vòng 24h.
                    </p>`
                        );
                        changeStep();
                    } else {
                        HoldOn.close();
                        toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                    }
                },
                error: function() {
                    HoldOn.close();
                    toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                }
            });
        }

        function checkIsAddMoreCert() {
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type') || "";
            const id = urlParams.get('uid') || "";
            if (type === 'add') {
                isAddMore = true;
                uid = id;
                const cid = urlParams.get('cid') || "";
                disabledCerts = cid.split(',');
                currentStep = 1;
                localStorage.setItem('currentStep', currentStep);

                certificates.sort((a, b) => {
                    if (disabledCerts.includes(a.cid)) return -1;
                    if (disabledCerts.includes(b.cid)) return 1;
                    return 0;
                });

            }
        }

        function addMoreCert() {
            $.ajax({
                type: 'POST',
                url: "<?php echo site_url('apiSignUp/add_more_cert'); ?>",
                data: {
                    uid: uid,
                    product_packet: plan,
                    category_id: certId,
                    payment_status: selectedPayment,
                    payment_method: selectedBank,
                },
                cache: false,
                dataType: 'json',
                beforeSend: function() {
                    HoldOn.open({
                        theme: "sk-cube-grid"
                    });
                },
                success: function(output) {
                    if (output.error === false) {
                        HoldOn.close();
                        $('.step-4-invite').addClass('hidden');
                        $('.step-4-buttons a').toggleClass('hidden');
                        $('.step-4-content').html(
                            `<p class="text-gray-500 text-sm font-medium">
                        Bạn đã hoàn thành đăng ký luyện thi chứng chỉ <span class="font-bold">${selectedCert.category_name} ${selectedPremium ? 'Premium' : 'Lite'}</span> với phương thức <span class="font-bold">${selectedPayment == 'immediate' ? 'thanh toán ngay' : 'thanh toán sau'}</span>. Tài khoản của bạn sẽ được cập nhật sau khi chúng tôi xác nhận thông tin thanh toán của bạn trong vòng 24h.
                    </p>`
                        );
                        changeStep();
                    } else {
                        HoldOn.close();
                        toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                    }
                },
                error: function() {
                    HoldOn.close();
                    toasterMessage('error', 'Có lỗi xảy ra, vui lòng thử lại sau.');
                }
            });
        }

        // Login and Forgot Password Popup Functionality
        $(document).ready(function() {
            // Function to check login form validity and enable/disable confirm button
            function checkLoginFormValidity() {
                const email = $('#login-email').val().trim();
                const password = $('#login-password').val().trim();
                const confirmBtn = $('#loginConfirmBtn');

                if (email && password) {
                    confirmBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                } else {
                    confirmBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');
                }
            }

            // Function to check forgot password form validity and enable/disable confirm button
            function checkForgotPasswordFormValidity() {
                const email = $('#forgot-email').val().trim();
                const confirmBtn = $('#forgotConfirmBtn');

                if (email) {
                    confirmBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                } else {
                    confirmBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');
                }
            }

            // Show login modal when "Log in to enroll" is clicked
            $('#loginToEnrollBtn').click(function() {
                $('#loginModal').removeClass('hidden');
                $('#login-email').focus();
                // Check form validity when modal opens
                checkLoginFormValidity();
            });

            // Close login modal
            $('#closeLoginModal, #loginCancelBtn').click(function() {
                $('#loginModal').addClass('hidden');
                $('#loginForm')[0].reset();
                // Reset button state when modal closes
                checkLoginFormValidity();
            });

            // Toggle password visibility
            $('#togglePassword').click(function() {
                const passwordInput = $('#login-password');
                const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';
                passwordInput.attr('type', type);
            });

            // Add event listeners for login form inputs
            $('#login-email, #login-password').on('input keyup', function() {
                checkLoginFormValidity();
            });

            // Show forgot password modal
            $('#forgotPasswordLink').click(function(e) {
                e.preventDefault();
                $('#loginModal').addClass('hidden');
                $('#forgotPasswordModal').removeClass('hidden');
                // Check form validity when modal opens
                checkForgotPasswordFormValidity();
            });

            // Close forgot password modal
            $('#closeForgotPasswordModal, #forgotCancelBtn').click(function() {
                $('#forgotPasswordModal').addClass('hidden');
                $('#forgotPasswordForm')[0].reset();
                // Reset button state when modal closes
                checkForgotPasswordFormValidity();
            });

            // Back to login from forgot password
            $('#backToLoginBtn').click(function() {
                $('#forgotPasswordModal').addClass('hidden');
                $('#loginModal').removeClass('hidden');
                $('#forgotPasswordForm')[0].reset();
                // Check login form validity when returning to login
                checkLoginFormValidity();
            });

            // Add event listener for forgot password form input
            $('#forgot-email').on('input keyup', function() {
                checkForgotPasswordFormValidity();
            });

            // Handle login form submission
            $('#loginForm').submit(function(e) {
                e.preventDefault();
                const email = $('#login-email').val();
                const password = $('#login-password').val();

                if (!email || !password) {
                    sendReportNotification('error', 'Please enter both email and password');
                    return;
                }

                $.ajax({
                    type: 'POST',
                    url: "<?php echo site_url('login/verifyloginajax'); ?>",
                    data: {
                        email: email,
                        password: password
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        HoldOn.open({
                            theme: "sk-cube-grid"
                        });
                    },
                    success: function(response) {
                        HoldOn.close();
                        if (response.login === 'ok') {
                            $('#loginModal').addClass('hidden');
                            $('#user-name-display').text(response.name);
                            localStorage.setItem('user_email', email);
                            changeStep();
                        } else {
                            sendReportNotification('error', response.data || 'Login failed. Please check your credentials.');
                        }
                    },
                    error: function() {
                        HoldOn.close();
                        sendReportNotification('error', 'An error occurred. Please try again later.');
                    }
                });
            });

            // Handle forgot password form submission
            $('#forgotPasswordForm').submit(function(e) {
                e.preventDefault();
                const email = $('#forgot-email').val();

                if (!email) {
                    toasterMessage('error', 'Please enter your email');
                    return;
                }

                $.ajax({
                    type: 'POST',
                    url: "<?php echo site_url('login/forgot_password'); ?>",
                    data: {
                        email: email
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        HoldOn.open({
                            theme: "sk-cube-grid"
                        });
                    },
                    success: function(response) {
                        HoldOn.close();
                        if (response.status === 'success') {
                            sendReportNotification('success', 'We’ve sent you an email — please follow the instructions to reset your password.');
                            $('#forgotPasswordModal').addClass('hidden');
                        } else {
                            sendReportNotification('error', response.message || 'Failed to process your request. Please try again.');
                        }
                    },
                    error: function() {
                        HoldOn.close();
                        toasterMessage('error', 'An error occurred. Please try again later.');
                    }
                });
            });
        });
    });
</script>

<!-- Login Popup -->
<div id="loginModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg w-[416px] shadow-lg relative">
        <!-- Header -->
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
            <div class="flex items-center">
                <h3 class="text-xl font-bold text-gray-900">Login</h3>
            </div>
            <button id="closeLoginModal" class="focus:outline-none">
                <img src="<?= base_url() ?>/images/icons/close_icon.svg" alt="Close" class="w-3 h-3">
            </button>
        </div>

        <!-- Body -->
        <div class="p-8">
            <form id="loginForm" class="space-y-6">
                <!-- Email Field -->
                <div>
                    <label for="login-email" class="block text-sm font-medium text-gray-900 mb-2">Email</label>
                    <div class="relative">
                        <input type="email" id="login-email" name="login-email" required
                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-1 focus:border-green-500">
                    </div>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="login-password" class="block text-sm font-medium text-gray-900 mb-2">Password</label>
                    <div class="relative">
                        <input type="password" id="login-password" name="password" required
                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-1 focus:border-green-500">
                        <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <img src="<?= base_url() ?>/images/icons/show_password_icon.svg" alt="Show Password" class="w-3 h-3">
                        </button>
                    </div>
                    <div class="text-left mt-2">
                        <a href="#" id="forgotPasswordLink" class="text-sm font-medium text-green-600 hover:text-green-500">Forgot password?</a>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex gap-4">
                    <button type="submit" id="loginConfirmBtn" class="bg-primary-blue-1 text-white py-2.5 px-5 rounded-lg font-medium transition-colors opacity-50 cursor-not-allowed" disabled>
                        Confirm
                    </button>
                    <button type="button" id="loginCancelBtn" class="border border-gray-200 text-gray-900 py-2.5 px-5 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Forgot Password Popup -->
<div id="forgotPasswordModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg w-[416px] shadow-lg relative">
        <!-- Header -->
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
            <div class="flex items-center">
                <button id="backToLoginBtn" class="mr-2">
                    <img src="<?= base_url() ?>/images/icons/back_arrow.svg" alt="Back" class="w-5 h-5">
                </button>
                <h3 class="text-xl font-bold text-gray-900">Forgot password</h3>
            </div>
            <button id="closeForgotPasswordModal" class="focus:outline-none">
                <img src="<?= base_url() ?>/images/icons/close_icon.svg" alt="Close" class="w-3 h-3">
            </button>
        </div>

        <!-- Body -->
        <div class="p-8">
            <form id="forgotPasswordForm" class="space-y-6">
                <!-- Email Field -->
                <div>
                    <label for="forgot-email" class="block text-sm font-medium text-gray-900 mb-2">Email</label>
                    <div class="relative">
                        <input type="email" id="forgot-email" name="email" required
                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-1 focus:border-green-500">
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex gap-4">
                    <button type="submit" id="forgotConfirmBtn" class="bg-primary-blue-1 text-white py-2.5 px-5 rounded-lg font-medium transition-colors opacity-50 cursor-not-allowed" disabled>
                        Confirm
                    </button>
                    <button type="button" id="forgotCancelBtn" class="border border-gray-200 text-gray-900 py-2.5 px-5 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

</html>