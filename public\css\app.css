@charset "UTF-8";
.big-banner {
  background-repeat: no-repeat;
  background-position: 0% 0%;
  background-size: 100% 100%;
  max-height: 306px;
}
.big-banner .grid .statistic {
  font-size: 32px;
  font-weight: 700;
  color: white;
}
.big-banner .chart {
  margin-top: 18px;
  margin-left: 9.5%;
  margin-right: 10%;
}

.study-assistant {
  box-shadow: 4px 4px 16px rgba(193, 211, 255, 0.26);
  border-radius: 20px;
}
.study-assistant .title {
  width: 25.3%;
  margin-right: 21px;
}
.study-assistant .progress {
  width: 56.7%;
  margin-right: 28px;
}
.study-assistant .progress .progress-bar {
  min-width: 10px;
}
.study-assistant .percent {
  width: 9.5%;
  text-align: end;
}

.radar-chart {
  box-shadow: 4px 4px 16px rgba(193, 211, 255, 0.26);
  border-radius: 20px;
  height: 260px;
}
.radar-chart .your-score {
  background: rgba(254, 201, 123, 0.33);
  border: 1px solid #fec97b;
  width: 28px;
  height: 17px;
  margin-right: 8px;
}
.radar-chart .your-score-text {
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  color: #B47110;
}
.radar-chart .recommend-score {
  width: 28px;
  height: 17px;
  background: rgba(216, 255, 246, 0.29);
  border: 1px solid #52cca7;
  margin-right: 8px;
}
.radar-chart .recommend-score-text {
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  color: #33A080;
}

.circle-progress {
  width: 132px;
  height: 132px;
}

:root {
  --color-green-primary: #12705B;
  --color-green-400: #31C48D;
  --color-green-500: #0E9F6E;
  --color-green-200: #BCF0DA;
  --color-green-700: #046C4E;
  --color-green-50: #F3FAF7;
}

.w-32 {
  width: 8rem;
}

.active-datepicker::-moz-placeholder {
  color: white !important;
}

.active-datepicker::placeholder {
  color: white !important;
}

.active-dropdown {
  background-color: #12705B !important;
  border: 3px solid #BCF0DA !important;
  color: white !important;
}

.active-dropdown svg {
  stroke: white !important;
}

#dropdown-menu ul li div:hover, #fontSizeDropdownMenu ul li div:hover {
  background-color: var(--color-green-50) !important;
}

#dropdown-menu label, #fontSizeDropdownMenu label {
  font-size: 14px !important;
}

.datepicker-controls button:disabled,
.datepicker-controls button:disabled:hover {
  background-color: #fff !important;
  cursor: not-allowed;
}

.circle-progress-value {
  stroke-width: 15px;
  stroke: #80f7ff;
  stroke-linecap: round;
}

.circle-progress-circle {
  stroke-width: 15px;
  stroke: white;
}

.circle-progress-text {
  display: none;
}

.unblur-chart-text {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-weight: bold;
  border: 3px solid #f1f1f1;
  position: absolute;
  top: 53%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 80%;
  padding: 20px;
  text-align: center;
}

.list-test,
.score-test,
.create-practice {
  box-shadow: 4px 4px 16px rgba(193, 211, 255, 0.26);
  border-radius: 20px;
}

.w-\[124px\] {
  width: 124px !important;
}

#logo-sidebar {
  transition: width 0.4s;
}

#logo-sidebar.closed {
  width: 76px;
}
#logo-sidebar.closed span {
  display: none;
}
#logo-sidebar.closed a {
  justify-content: center;
}
#logo-sidebar.closed .item-wrap {
  padding-left: 1rem;
  padding-right: 1rem;
}

.main-wrapper {
  transition: width 0.4s;
}
.main-wrapper.expanded {
  margin-left: 76px;
}

#countdown-time {
  color: #dc5c00;
}

.basic_container .line_bar {
  width: calc(100% - 8px);
  left: 4px;
}
.basic_container .num_item.selected input {
  width: 22px;
  height: 22px;
  border-width: 5px;
  border-color: #4776ed;
}
.basic_container .num_item.selected .num_text {
  color: #1c64f2;
}

.advance_container .domain_item.selected {
  color: #1a56db;
  border-color: #1a56db;
  background-color: #e1effe;
}

.mentor-button {
  background: linear-gradient(360deg, #2258DF 0%, #6691FF 104.72%);
  box-shadow: 0px 4px 6px 0px rgba(28, 100, 242, 0.1294117647), 0px 10px 15px -3px rgba(28, 100, 242, 0.3294117647);
}

.mentor-popup {
  box-shadow: 0px 4px 6px 0px rgba(88, 80, 236, 0.2784313725), 0px 10px 15px -3px rgba(88, 80, 236, 0.1019607843);
}

@media (min-width: 1280px) {
  .big-banner .grid .statistic {
    font-size: 40px;
  }
  .big-banner .grid .statistic-no-data {
    font-size: 40px;
  }
  .big-banner .chart {
    margin-right: 19%;
  }
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Màu đen mờ với độ mờ là 0.5 */
  z-index: 49; /* Sắp xếp lớp z-index thấp hơn so với modal */
}

#tooltip-pass-rate-icon {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(194deg) brightness(102%) contrast(103%);
}

#tooltip-quiz-done-icon {
  filter: brightness(0) saturate(100%) invert(33%) sepia(31%) saturate(676%) hue-rotate(65deg) brightness(96%) contrast(96%);
}

#tooltip-question-wrong-icon {
  filter: brightness(0) saturate(100%) invert(22%) sepia(97%) saturate(1714%) hue-rotate(21deg) brightness(103%) contrast(95%);
}

#tooltip-question-avg-icon {
  filter: brightness(0) saturate(100%) invert(36%) sepia(32%) saturate(390%) hue-rotate(146deg) brightness(97%) contrast(86%);
}

#tooltip-total-time-icon {
  filter: brightness(0) saturate(100%) invert(18%) sepia(92%) saturate(399%) hue-rotate(184deg) brightness(93%) contrast(94%);
}

.skeleton {
  color: grey;
  display: inline-block;
  -webkit-mask: linear-gradient(-60deg, #000 30%, rgba(0, 0, 0, 0.3333333333), #000 70%) right/300% 100%;
  background-repeat: no-repeat;
  animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
table.dataTable tbody tr td:nth-child(5) {
  color: #111928;
  font-weight: 600;
}

#dropdownRadioButton.active {
  color: white;
  background-color: #4776ED;
}
#dropdownRadioButton.active img, #dropdownRadioButton.active svg {
  filter: invert(100%) sepia(78%) saturate(2%) hue-rotate(259deg) brightness(102%) contrast(101%);
}
#dropdownRadioButton.active svg {
  transform: rotate(180deg);
}

#dropdownRadioButton img {
  filter: invert(80%) sepia(10%) saturate(324%) hue-rotate(179deg) brightness(80%) contrast(90%);
}
#dropdownRadioButton svg {
  filter: invert(9%) sepia(40%) saturate(805%) hue-rotate(171deg) brightness(94%) contrast(83%);
}

#dropdownRadioButton2.active {
  color: white;
  background-color: #4776ED;
}
#dropdownRadioButton2.active img, #dropdownRadioButton2.active svg {
  filter: invert(100%) sepia(78%) saturate(2%) hue-rotate(259deg) brightness(200%) contrast(151%);
}

table.dataTable tbody tr.continue,
table.dataTable tbody tr.continue:hover {
  background-color: #FFF8F1;
}

.radio-normal,
.checkbox-normal {
  filter: invert(69%) sepia(22%) saturate(152%) hue-rotate(179deg) brightness(92%) contrast(84%);
}

.radio-correct,
.checkbox-correct {
  filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.radio-correct-progress,
.checkbox-correct-progress {
  filter: invert(42%) sepia(99%) saturate(3359%) hue-rotate(146deg) brightness(93%) contrast(94%);
}

.mark_right_answer img {
  filter: brightness(0) saturate(100%) invert(25%) sepia(66%) saturate(2173%) hue-rotate(143deg) brightness(95%) contrast(96%);
}

.mark_wrong_answer img {
  filter: brightness(0) saturate(100%) invert(33%) sepia(56%) saturate(7373%) hue-rotate(347deg) brightness(88%) contrast(100%);
}

.mark-active-indicator,
.active-indicator {
  background-color: #4776ed !important;
  color: #fff !important;
}
.mark-active-indicator img,
.active-indicator img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(6%) saturate(2%) hue-rotate(209deg) brightness(113%) contrast(100%) !important;
}

.previous-question img,
.next-question img {
  filter: brightness(0) saturate(100%) invert(51%) sepia(39%) saturate(7080%) hue-rotate(210deg) brightness(96%) contrast(94%);
}
.previous-question.disabled img,
.next-question.disabled img {
  filter: brightness(0) saturate(100%) invert(89%) sepia(4%) saturate(262%) hue-rotate(177deg) brightness(92%) contrast(100%);
}

.next-question img {
  transform: rotate(180deg);
}

.progress .progressbar__label {
  font-size: 18px;
  transform: translate(-50%, 25%) !important;
  color: #057A55 !important;
  font-weight: 700;
}
.progress .progressbar__label span {
  font-size: 14px;
}
.progress .progressbar__labelfail {
  font-size: 18px;
  transform: translate(-50%, 25%) !important;
  color: #E02424 !important;
  font-weight: 700;
}
.progress .progressbar__labelfail span {
  font-size: 14px;
}
.progress svg {
  height: 75px;
  width: 150px;
  fill: none;
  stroke-width: 15;
  margin: 0 auto;
}

.op b {
  margin-right: 5px;
}

.question p {
  display: inline;
}

.fade-out {
  position: relative;
}

.fade-out::before {
  content: ""; /* Đặt nội dung của pseudo-element */
  position: absolute; /* Đặt vị trí của pseudo-element là absolute */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 40%); /* Gradient background với khoảng cách mờ 10% */
  pointer-events: none; /* Đặt sự kiện con trỏ là không */
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.question-section {
  height: calc(100% - 131px);
}
.question-section .question-container {
  overflow: auto;
}
.question-section .question-container .question-text table {
  width: 100%;
}
.question-section .question-container .question-text table,
.question-section .question-container .question-text td,
.question-section .question-container .question-text th {
  border: 1px solid #9ca3af;
}
.question-section .question-list .flex-wrap {
  overflow-y: auto;
}
.question-section .option_container .options::first-letter {
  text-transform: uppercase;
}
.question-section .option_container .options p {
  display: inline-block;
}

.progress-bar {
  height: 3px;
  width: 0;
}

#zoom-dropdown img {
  filter: brightness(0) saturate(100%) invert(46%) sepia(9%) saturate(622%) hue-rotate(182deg) brightness(93%) contrast(87%);
}

.navigator-section {
  width: calc(100% - 32px);
}
.navigator-section .clear-btn.disable {
  cursor: not-allowed;
  background-color: #d1d5db;
  color: white;
  border-color: #d1d5db;
}
.navigator-section .clear-btn.disable img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(53deg) brightness(103%) contrast(103%);
}
.navigator-section .next-btn,
.navigator-section .back-btn,
.navigator-section .result-btn {
  background-color: #fff;
  color: #4776ed;
  border-color: #4776ed;
}
.navigator-section .next-btn img,
.navigator-section .back-btn img,
.navigator-section .result-btn img {
  filter: brightness(0) saturate(100%) invert(39%) sepia(45%) saturate(3072%) hue-rotate(211deg) brightness(99%) contrast(88%);
}
.navigator-section .next-btn.disable,
.navigator-section .back-btn.disable,
.navigator-section .result-btn.disable {
  cursor: not-allowed;
  background-color: #d1d5db;
  color: white;
  border-color: #d1d5db;
}
.navigator-section .next-btn.disable img,
.navigator-section .back-btn.disable img,
.navigator-section .result-btn.disable img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(53deg) brightness(103%) contrast(103%);
}
.navigator-section .clear-btn img {
  filter: brightness(0) saturate(100%) invert(46%) sepia(9%) saturate(622%) hue-rotate(182deg) brightness(93%) contrast(87%);
}

.bookmark-icon {
  filter: brightness(0) saturate(100%) invert(96%) sepia(100%) saturate(0%) hue-rotate(52deg) brightness(105%) contrast(103%);
}

b,
strong {
  font-weight: 800 !important;
}

.answer_row:hover input {
  --tw-border-opacity: 1;
  border-color: #111928;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}

.answer_row input:checked {
  --tw-border-opacity: 1;
  border-color: #4776ED;
  color: #4776ED !important;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}

.answer_row:hover input:checked {
  --tw-border-opacity: 1;
  border-color: #76A9FA;
  color: #76A9FA !important;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}

.answer_row:has(input:checked) {
  background-color: #FFFFFF;
  border-color: #4776ED;
}

.answer_row:hover {
  background-color: #F3F4F6;
  border-color: #111928;
}

.answer_row:hover:has(input:checked) {
  background-color: #FFFFFF;
  border-color: #76A9FA;
}

.radio,
.checkbox {
  vertical-align: top;
}

.fade-out {
  position: relative;
}

.fade-out::before {
  content: ""; /* Đặt nội dung của pseudo-element */
  position: absolute; /* Đặt vị trí của pseudo-element là absolute */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 40%); /* Gradient background với khoảng cách mờ 10% */
  pointer-events: none; /* Đặt sự kiện con trỏ là không */
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.full-screen-popup {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgb(255, 255, 255);
  z-index: 99;
}

.flex-container-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
}

.flex-container-center h2 {
  text-align: center;
  color: #858796;
}

.full-screen {
  color: black;
}

.hidden {
  display: none;
}

.image-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.text-color-gray-900 {
  color: #111928;
}

.text-color-gray-800 {
  color: #1F2A37;
}

.image-wrapper img {
  display: flex;
  width: 300px;
}

.image-wrapper a {
  margin: 20px;
}

.flex-container-center button {
  margin-top: 15px;
  border-radius: 0.35rem;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.custom-alert-msg-wrap.alert.alert-danger {
  background-color: #fdf2f2;
  color: #f05252;
  padding: 20px;
}

.custom-alert-msg-wrap.alert.alert-success {
  background-color: #d8ffdc;
  color: #057a55;
  padding: 20px;
}

.datepicker-controls button:disabled svg {
  filter: brightness(0) saturate(100%) invert(88%) sepia(8%) saturate(149%) hue-rotate(177deg) brightness(100%) contrast(85%);
}

.datepicker-cell.disabled {
  --tw-text-opacity: 1;
  color: rgb(209 213 219/var(--tw-text-opacity));
}

:root {
  --swiper-theme-color: #D9D9D9 !important;
  --color-gray-900: #111928;
  --color-blue-primary: #4776ED;
}

.certificate-list {
  max-height: calc(100vh - 280px);
}

.cert-item span {
  height: 42px;
  line-height: 21px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
}

input.has-error,
select.has-error {
  border-color: #F05252;
  color: #C81E1E;
  background-color: #FDF2F2;
}
input.has-error:focus,
select.has-error:focus {
  border-color: #D32F2F;
  --tw-ring-color: rgb(240 82 82 / var(--tw-ring-opacity)) ;
}

.final-step-bg {
  background: linear-gradient(180deg, #FFFFFF 0%, #E7EFFC 100%);
}

.main-final-step-content {
  max-height: calc(100vh - 81px);
  overflow-y: auto;
}

.\!text-\[14px\] {
  font-size: 14px !important;
}

.cert-item.selected {
  border-color: #4776ED;
}
.cert-item.selected::before {
  content: "";
  background: url("/images/new/selected.png") no-repeat top right;
  background-size: 20px 20px;
  position: absolute;
  top: -1px;
  right: -1px;
  width: 20px;
  height: 20px;
}

.bank-option.selected {
  border-color: #4776ED;
}
.bank-option.selected::before {
  content: "";
  background: url("/images/new/selected.png") no-repeat top right;
  background-size: 14px 14px;
  position: absolute;
  top: -1px;
  right: -1px;
  width: 14px;
  height: 14px;
}

.next-btn.active {
  background-color: #4776ED;
}

li.current-step {
  color: #111928;
}

li.step-done {
  color: #111928;
}
li.step-done::after {
  background: #0E9F6E;
}

#autoComplete_list_1 {
  background-color: #FFFFFF;
  border-width: 1px;
  border-color: #E5E7EB;
  padding: 8px;
  border-radius: 8px;
  margin-top: 8px;
  position: absolute;
  left: 0;
  top: 46px;
  width: 100%;
  z-index: 10;
}
#autoComplete_list_1 li {
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
  text-underline-position: from-font;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
  color: #6B7280;
  border-radius: 8px;
  padding-left: 30px;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-right: 8px;
  position: relative;
  cursor: pointer;
}
#autoComplete_list_1 li:hover {
  background-color: #EBF5FF;
}
#autoComplete_list_1 li::before {
  content: "";
  background: url("/images/icons/search.svg") no-repeat center center;
  background-size: 14px 14px;
  position: absolute;
  left: 8px;
  top: 7.5px;
  width: 14px;
  height: 14px;
}
#autoComplete_list_1 li mark {
  background-color: transparent;
  color: #4776ED;
}

@media (max-width: 375px) {
  .certificate-list {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
}
@media (min-width: 1024px) {
  .step-2 {
    max-height: calc(100vh - 165px);
  }
  .copy-qr {
    display: none !important;
  }
  .certificate-list {
    max-height: calc(100vh - 295px);
    padding-bottom: 0.75rem;
  }
}
button.copy-bank-account,
button.copy-bank-content {
  cursor: pointer;
}

.cert-item {
  width: auto;
  height: 164px;
}

.cert-item img {
  height: auto !important;
  width: 106px !important;
}

@media (max-width: 600px) {
  .cert-item {
    height: 116px !important;
  }
  .cert-item span {
    font-size: 12px;
    line-height: 18px !important;
    height: 36px;
  }
  .cert-item img {
    width: 64px !important;
    height: auto !important;
  }
  .logo-last-step {
    margin: 0 !important;
  }
}
@media (max-width: 1023px) {
  .qr-bank-img {
    width: 100%;
  }
  .qr-wrapper {
    width: 275px !important;
    margin: 0 auto;
    height: auto !important;
  }
  .bank-content-wrapper {
    gap: 0.75rem !important;
  }
  #paymentModal .payment-pop-up {
    width: 343px;
  }
  #paymentModal .list-bank {
    flex-direction: column;
    align-items: flex-start;
  }
  #paymentModal .list-bank-button {
    gap: 0.75rem;
  }
  #paymentModal h3.bank-info-title {
    font-size: 18px;
    line-height: 23.4px;
  }
  #paymentModal .bank-option.bank-item,
  #paymentModal .bank-option.bank-item img {
    max-width: 64px;
    width: 64px;
    height: 30px;
  }
  #paymentModal div.payment-info {
    padding: 1.5rem 1rem;
  }
  #paymentModal img.qr-bank-img {
    max-width: 100%;
  }
}
@media (max-width: 1024px) and (min-width: 601px) {
  .certificate-list {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    max-height: calc(100vh - 304px);
  }
}
@media (max-width: 600px) {
  #tooltip-animation, #tooltip-address {
    font-size: 13px !important;
  }
  .step-1 .certificate-list {
    display: none;
  }
  .step-1 .swiper.certSwiper {
    display: block;
  }
  a#openModal {
    font-size: 14px;
  }
}
.swiper.certSwiper {
  display: none;
}

button.copy-bank-content img {
  margin-bottom: -4px !important;
}

.datepicker .datepicker-controls button.next-btn.active {
  background-color: white !important;
}

.datepicker .datepicker-controls button.next-btn.active:hover {
  background-color: rgb(243 244 246/var(--tw-bg-opacity)) !important;
}

.copy-qr {
  padding: 8px 20px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  justify-content: center;
  width: 275px;
  align-self: center;
}

.copy-qr span {
  color: #1F2A37;
}

.search-box svg {
  display: none;
}

.search-box:focus-within svg {
  display: block;
}

.search-box:focus-within img {
  display: none;
}

.swiper {
  width: 100%;
  height: 100%;
  margin-bottom: 8px;
}

.swiper-slide {
  box-sizing: border-box;
}

.swiper-pagination {
  position: relative !important;
  height: 8px !important;
}

.swiper-pagination-bullet,
.swiper-pagination-bullet-active {
  width: 10px !important;
  height: 10px !important;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #9CA3AF !important;
  background-color: #9CA3AF !important;
  border: none !important;
  padding: 1px;
}

.swiper-pagination-bullet {
  background: #fff !important;
  border: 1px solid #9CA3AF !important;
}

button#closeModal {
  top: 26.5px;
}

#paymentModal .payment-info {
  padding: 32px;
  padding-top: 24px;
  color: var(--color-gray-900) !important;
}

#paymentModal .bank-info-title {
  font-size: 20px;
  line-height: 25px;
}

#paymentModal .choose-bank {
  font-size: 14px;
}

#paymentModal .bank-content-wrapper p {
  line-height: 21px;
}

#paymentModal .copy-bank-account {
  margin-top: -2px;
}

#paymentModal .qr-wrapper {
  height: 120px;
}

#paymentModal .qr-bank-img {
  max-width: 120px;
}

#paymentModal .bank-option img {
  max-width: 70px;
  width: 70px;
  height: 34px;
}

a#openModal {
  color: var(--color-blue-primary);
}

.disabled-cert .gray-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(243, 244, 246, 0.7);
  pointer-events: none;
}

button:disabled {
  background: var(--gray-300, #D1D5DB) !important;
}

button:disabled:hover {
  background: var(--gray-300, #D1D5DB) !important;
}

/* width */
::-webkit-scrollbar {
  width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #EAECF0;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #98A2B3;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
  border-radius: 10px;
}

.triangle {
  display: block;
  height: 15px;
  width: 15px;
  background-color: inherit;
  border: inherit;
  bottom: -10px;
  left: calc(50% - 10px);
  -webkit-clip-path: polygon(0% 0%, 100% 100%, 0% 100%);
          clip-path: polygon(0% 0%, 100% 100%, 0% 100%);
  transform: rotate(-45deg);
  border-radius: 0 0 0 0.25em;
  background-color: #E5E7EB;
  rotate: 180deg;
  margin-left: 25px;
}

.custom-input:checked {
  background-color: #1C64F2 !important;
  background-size: 140% 140% !important;
}

.filter-none {
  filter: none !important;
}
/*
! tailwindcss v3.3.2 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #E5E7EB; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
*/

html {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Inter, Helvetica, Arial, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9CA3AF; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9CA3AF; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #1C64F2;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6B7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6B7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
}

select:not([size]) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple] {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #1C64F2;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px auto inherit;
}

input[type=file]::file-selector-button {
  color: white;
  background: #1F2937;
  border: 0;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 2rem;
  padding-right: 1rem;
  -webkit-margin-start: -1rem;
          margin-inline-start: -1rem;
  -webkit-margin-end: 1rem;
          margin-inline-end: 1rem;
}

input[type=file]::file-selector-button:hover {
  background: #374151;
}

.dark input[type=file]::file-selector-button {
  color: white;
  background: #4B5563;
}

.dark input[type=file]::file-selector-button:hover {
  background: #6B7280;
}

input[type="range"]::-webkit-slider-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-webkit-slider-thumb {
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-webkit-slider-thumb {
  background: #6B7280;
}

input[type="range"]:focus::-webkit-slider-thumb {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1px;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}

input[type="range"]::-moz-range-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-moz-range-thumb {
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-moz-range-thumb {
  background: #6B7280;
}

input[type="range"]::-moz-range-progress {
  background: #3F83F8;
}

input[type="range"]::-ms-fill-lower {
  background: #3F83F8;
}

.toggle-bg:after {
  content: "";
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  background: white;
  border-color: #D1D5DB;
  border-width: 1px;
  border-radius: 9999px;
  height: 1.25rem;
  width: 1.25rem;
  transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;
  transition-duration: .15s;
  box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

input:checked + .toggle-bg:after {
  transform: translateX(100%);;
  border-color: white;
}

input:checked + .toggle-bg {
  background: #1C64F2;
  border-color: #1C64F2;
}

.tooltip-arrow,.tooltip-arrow:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

.tooltip-arrow {
  visibility: hidden;
}

.tooltip-arrow:before {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
}

[data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before {
  border-style: solid;
  border-color: #e5e7eb;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

.tooltip[data-popper-placement^='top'] > .tooltip-arrow {
  bottom: -4px;
}

.tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
  top: -4px;
}

.tooltip[data-popper-placement^='left'] > .tooltip-arrow {
  right: -4px;
}

.tooltip[data-popper-placement^='right'] > .tooltip-arrow {
  left: -4px;
}

.tooltip.invisible > .tooltip-arrow:before {
  visibility: hidden;
}

[data-popper-arrow],[data-popper-arrow]:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

[data-popper-arrow] {
  visibility: hidden;
}

[data-popper-arrow]:before {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
}

[data-popper-arrow]:after {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
  position: absolute;
  width: 9px;
  height: 9px;
  background: inherit;
}

[role="tooltip"] > [data-popper-arrow]:before {
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:before {
  border-style: solid;
  border-color: #4b5563;
}

[role="tooltip"] > [data-popper-arrow]:after {
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:after {
  border-style: solid;
  border-color: #4b5563;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow] {
  bottom: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow] {
  top: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow] {
  right: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow] {
  left: -5px;
}

[role="tooltip"].invisible > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].invisible > [data-popper-arrow]:after {
  visibility: hidden;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}
.\!container {
  width: 100% !important;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.\!visible {
  visibility: visible !important;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-0 {
  bottom: -0px;
}
.-bottom-0\.5 {
  bottom: -0.125rem;
}
.-left-2 {
  left: -0.5rem;
}
.-left-20 {
  left: -5rem;
}
.-left-\[18px\] {
  left: -18px;
}
.-right-20 {
  right: -5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-5 {
  bottom: 1.25rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-8 {
  bottom: 2rem;
}
.bottom-\[10px\] {
  bottom: 10px;
}
.bottom-\[20px\] {
  bottom: 20px;
}
.bottom-\[40px\] {
  bottom: 40px;
}
.bottom-\[60px\] {
  bottom: 60px;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-12 {
  left: 3rem;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-\[-3px\] {
  left: -3px;
}
.left-\[130px\] {
  left: 130px;
}
.left-\[13px\] {
  left: 13px;
}
.left-\[20vw\] {
  left: 20vw;
}
.left-\[245px\] {
  left: 245px;
}
.left-\[24px\] {
  left: 24px;
}
.left-\[43\%\] {
  left: 43%;
}
.left-\[5vw\] {
  left: 5vw;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-2 {
  right: 0.5rem;
}
.right-2\.5 {
  right: 0.625rem;
}
.right-24 {
  right: 6rem;
}
.right-3 {
  right: 0.75rem;
}
.right-5 {
  right: 1.25rem;
}
.right-6 {
  right: 1.5rem;
}
.right-8 {
  right: 2rem;
}
.right-\[100px\] {
  right: 100px;
}
.right-\[20px\] {
  right: 20px;
}
.right-\[23px\] {
  right: 23px;
}
.right-\[25px\] {
  right: 25px;
}
.right-\[5vw\] {
  right: 5vw;
}
.right-\[60px\] {
  right: 60px;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-3 {
  top: 0.75rem;
}
.top-3\.5 {
  top: 0.875rem;
}
.top-5 {
  top: 1.25rem;
}
.top-6 {
  top: 1.5rem;
}
.top-\[0\.55rem\] {
  top: 0.55rem;
}
.top-\[10px\] {
  top: 10px;
}
.top-\[15vh\] {
  top: 15vh;
}
.top-\[16px\] {
  top: 16px;
}
.top-\[17px\] {
  top: 17px;
}
.top-\[20px\] {
  top: 20px;
}
.top-\[23px\] {
  top: 23px;
}
.top-\[24px\] {
  top: 24px;
}
.top-\[25px\] {
  top: 25px;
}
.top-\[38vh\] {
  top: 38vh;
}
.top-\[40\%\] {
  top: 40%;
}
.top-\[58\%\] {
  top: 58%;
}
.isolate {
  isolation: isolate;
}
.-z-\[1\] {
  z-index: -1;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[1000000\] {
  z-index: 1000000;
}
.z-\[100\] {
  z-index: 100;
}
.z-\[1\] {
  z-index: 1;
}
.z-\[40\] {
  z-index: 40;
}
.z-\[500\] {
  z-index: 500;
}
.col-auto {
  grid-column: auto;
}
.float-right {
  float: right;
}
.float-left {
  float: left;
}
.clear-both {
  clear: both;
}
.\!m-0 {
  margin: 0px !important;
}
.m-0 {
  margin: 0px;
}
.m-1 {
  margin: 0.25rem;
}
.m-1\.5 {
  margin: 0.375rem;
}
.m-\[20px\] {
  margin: 20px;
}
.m-auto {
  margin: auto;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.-mx-1\.5 {
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.-my-1 {
  margin-top: -0.25rem;
  margin-bottom: -0.25rem;
}
.-my-1\.5 {
  margin-top: -0.375rem;
  margin-bottom: -0.375rem;
}
.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}
.mx-0\.5 {
  margin-left: 0.125rem;
  margin-right: 0.125rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-2\.5 {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-8 {
  margin-left: 2rem;
  margin-right: 2rem;
}
.mx-\[16px\] {
  margin-left: 16px;
  margin-right: 16px;
}
.mx-\[20px\] {
  margin-left: 20px;
  margin-right: 20px;
}
.mx-\[6px\] {
  margin-left: 6px;
  margin-right: 6px;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-\[20px\] {
  margin-top: 20px;
  margin-bottom: 20px;
}
.my-\[24px\] {
  margin-top: 24px;
  margin-bottom: 24px;
}
.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}
.\!mr-\[6px\] {
  margin-right: 6px !important;
}
.\!mt-\[16px\] {
  margin-top: 16px !important;
}
.-mb-px {
  margin-bottom: -1px;
}
.-mt-3 {
  margin-top: -0.75rem;
}
.-mt-\[15px\] {
  margin-top: -15px;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-\[10px\] {
  margin-bottom: 10px;
}
.mb-\[12px\] {
  margin-bottom: 12px;
}
.mb-\[16px\] {
  margin-bottom: 16px;
}
.mb-\[1px\] {
  margin-bottom: 1px;
}
.mb-\[20px\] {
  margin-bottom: 20px;
}
.mb-\[2px\] {
  margin-bottom: 2px;
}
.mb-\[30px\] {
  margin-bottom: 30px;
}
.mb-\[40px\] {
  margin-bottom: 40px;
}
.mb-\[46px\] {
  margin-bottom: 46px;
}
.mb-\[4px\] {
  margin-bottom: 4px;
}
.mb-\[5px\] {
  margin-bottom: 5px;
}
.mb-\[6px\] {
  margin-bottom: 6px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-\[10px\] {
  margin-left: 10px;
}
.ml-\[12px\] {
  margin-left: 12px;
}
.ml-\[14px\] {
  margin-left: 14px;
}
.ml-\[20px\] {
  margin-left: 20px;
}
.ml-\[21px\] {
  margin-left: 21px;
}
.ml-\[42px\] {
  margin-left: 42px;
}
.ml-\[4px\] {
  margin-left: 4px;
}
.ml-\[5px\] {
  margin-left: 5px;
}
.ml-\[6px\] {
  margin-left: 6px;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-10 {
  margin-right: 2.5rem;
}
.mr-16 {
  margin-right: 4rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-28 {
  margin-right: 7rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-6 {
  margin-right: 1.5rem;
}
.mr-8 {
  margin-right: 2rem;
}
.mr-\[10px\] {
  margin-right: 10px;
}
.mr-\[12px\] {
  margin-right: 12px;
}
.mr-\[16px\] {
  margin-right: 16px;
}
.mr-\[20px\] {
  margin-right: 20px;
}
.mr-\[32px\] {
  margin-right: 32px;
}
.mr-\[5px\] {
  margin-right: 5px;
}
.mr-\[6\%\] {
  margin-right: 6%;
}
.mr-\[6px\] {
  margin-right: 6px;
}
.mr-\[8px\] {
  margin-right: 8px;
}
.mr-auto {
  margin-right: auto;
}
.ms-2 {
  -webkit-margin-start: 0.5rem;
          margin-inline-start: 0.5rem;
}
.ms-auto {
  -webkit-margin-start: auto;
          margin-inline-start: auto;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-14 {
  margin-top: 3.5rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-2\.5 {
  margin-top: 0.625rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-7 {
  margin-top: 1.75rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-\[10px\] {
  margin-top: 10px;
}
.mt-\[12px\] {
  margin-top: 12px;
}
.mt-\[16px\] {
  margin-top: 16px;
}
.mt-\[1px\] {
  margin-top: 1px;
}
.mt-\[20px\] {
  margin-top: 20px;
}
.mt-\[30px\] {
  margin-top: 30px;
}
.mt-\[3px\] {
  margin-top: 3px;
}
.mt-\[40px\] {
  margin-top: 40px;
}
.mt-\[4px\] {
  margin-top: 4px;
}
.mt-\[5px\] {
  margin-top: 5px;
}
.mt-\[6px\] {
  margin-top: 6px;
}
.mt-\[8px\] {
  margin-top: 8px;
}
.mt-auto {
  margin-top: auto;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.inline-table {
  display: inline-table;
}
.table-caption {
  display: table-caption;
}
.table-cell {
  display: table-cell;
}
.\!grid {
  display: grid !important;
}
.grid {
  display: grid;
}
.contents {
  display: contents;
}
.\!hidden {
  display: none !important;
}
.hidden {
  display: none;
}
.\!h-\[20px\] {
  height: 20px !important;
}
.\!h-\[24px\] {
  height: 24px !important;
}
.\!h-\[90\%\] {
  height: 90% !important;
}
.h-0 {
  height: 0px;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-28 {
  height: 7rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-36 {
  height: 9rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-60 {
  height: 15rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[10px\] {
  height: 10px;
}
.h-\[132px\] {
  height: 132px;
}
.h-\[15px\] {
  height: 15px;
}
.h-\[163px\] {
  height: 163px;
}
.h-\[16px\] {
  height: 16px;
}
.h-\[170px\] {
  height: 170px;
}
.h-\[186px\] {
  height: 186px;
}
.h-\[1px\] {
  height: 1px;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[20px\] {
  height: 20px;
}
.h-\[24px\] {
  height: 24px;
}
.h-\[250px\] {
  height: 250px;
}
.h-\[25px\] {
  height: 25px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[32px\] {
  height: 32px;
}
.h-\[34px\] {
  height: 34px;
}
.h-\[36px\] {
  height: 36px;
}
.h-\[40px\] {
  height: 40px;
}
.h-\[41px\] {
  height: 41px;
}
.h-\[450px\] {
  height: 450px;
}
.h-\[45px\] {
  height: 45px;
}
.h-\[480px\] {
  height: 480px;
}
.h-\[52px\] {
  height: 52px;
}
.h-\[5px\] {
  height: 5px;
}
.h-\[67px\] {
  height: 67px;
}
.h-\[70px\] {
  height: 70px;
}
.h-\[76px\] {
  height: 76px;
}
.h-\[80px\] {
  height: 80px;
}
.h-\[93\%\] {
  height: 93%;
}
.h-\[calc\(100\%-1rem\)\] {
  height: calc(100% - 1rem);
}
.h-auto {
  height: auto;
}
.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-16 {
  max-height: 4rem;
}
.max-h-52 {
  max-height: 13rem;
}
.max-h-7 {
  max-height: 1.75rem;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-\[184px\] {
  max-height: 184px;
}
.max-h-full {
  max-height: 100%;
}
.min-h-\[150px\] {
  min-height: 150px;
}
.min-h-\[18px\] {
  min-height: 18px;
}
.min-h-\[24px\] {
  min-height: 24px;
}
.min-h-screen {
  min-height: 100vh;
}
.\!w-\[20px\] {
  width: 20px !important;
}
.\!w-\[24px\] {
  width: 24px !important;
}
.w-0 {
  width: 0px;
}
.w-0\.5 {
  width: 0.125rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-1\/4 {
  width: 25%;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-2\/4 {
  width: 50%;
}
.w-2\/6 {
  width: 33.333333%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\/12 {
  width: 25%;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-4\/12 {
  width: 33.333333%;
}
.w-40 {
  width: 10rem;
}
.w-44 {
  width: 11rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-52 {
  width: 13rem;
}
.w-6 {
  width: 1.5rem;
}
.w-6\/12 {
  width: 50%;
}
.w-60 {
  width: 15rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-8\/12 {
  width: 66.666667%;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[10\%\] {
  width: 10%;
}
.w-\[10\.2\%\] {
  width: 10.2%;
}
.w-\[10\.3\%\] {
  width: 10.3%;
}
.w-\[100\%\] {
  width: 100%;
}
.w-\[1000px\] {
  width: 1000px;
}
.w-\[11\%\] {
  width: 11%;
}
.w-\[12\%\] {
  width: 12%;
}
.w-\[124px\] {
  width: 124px;
}
.w-\[13\%\] {
  width: 13%;
}
.w-\[132px\] {
  width: 132px;
}
.w-\[149px\] {
  width: 149px;
}
.w-\[15\%\] {
  width: 15%;
}
.w-\[150px\] {
  width: 150px;
}
.w-\[16px\] {
  width: 16px;
}
.w-\[17\%\] {
  width: 17%;
}
.w-\[18\%\] {
  width: 18%;
}
.w-\[20\.5\%\] {
  width: 20.5%;
}
.w-\[20px\] {
  width: 20px;
}
.w-\[214px\] {
  width: 214px;
}
.w-\[220px\] {
  width: 220px;
}
.w-\[239px\] {
  width: 239px;
}
.w-\[24px\] {
  width: 24px;
}
.w-\[250px\] {
  width: 250px;
}
.w-\[25px\] {
  width: 25px;
}
.w-\[260px\] {
  width: 260px;
}
.w-\[270px\] {
  width: 270px;
}
.w-\[290px\] {
  width: 290px;
}
.w-\[30\%\] {
  width: 30%;
}
.w-\[300px\] {
  width: 300px;
}
.w-\[310px\] {
  width: 310px;
}
.w-\[311px\] {
  width: 311px;
}
.w-\[320px\] {
  width: 320px;
}
.w-\[32px\] {
  width: 32px;
}
.w-\[352px\] {
  width: 352px;
}
.w-\[356px\] {
  width: 356px;
}
.w-\[36px\] {
  width: 36px;
}
.w-\[372px\] {
  width: 372px;
}
.w-\[380px\] {
  width: 380px;
}
.w-\[383px\] {
  width: 383px;
}
.w-\[40px\] {
  width: 40px;
}
.w-\[410px\] {
  width: 410px;
}
.w-\[416px\] {
  width: 416px;
}
.w-\[48\%\] {
  width: 48%;
}
.w-\[5\%\] {
  width: 5%;
}
.w-\[52px\] {
  width: 52px;
}
.w-\[546px\] {
  width: 546px;
}
.w-\[5px\] {
  width: 5px;
}
.w-\[60\%\] {
  width: 60%;
}
.w-\[70px\] {
  width: 70px;
}
.w-\[73\.3\%\] {
  width: 73.3%;
}
.w-\[75px\] {
  width: 75px;
}
.w-\[8\%\] {
  width: 8%;
}
.w-\[8\.1\%\] {
  width: 8.1%;
}
.w-\[8\.3\%\] {
  width: 8.3%;
}
.w-\[80\%\] {
  width: 80%;
}
.w-\[80px\] {
  width: 80px;
}
.w-\[840px\] {
  width: 840px;
}
.w-\[84px\] {
  width: 84px;
}
.w-\[88px\] {
  width: 88px;
}
.w-\[9\%\] {
  width: 9%;
}
.w-\[90px\] {
  width: 90px;
}
.w-\[93\%\] {
  width: 93%;
}
.w-\[93\.6\%\] {
  width: 93.6%;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.w-screen {
  width: 100vw;
}
.min-w-\[240px\] {
  min-width: 240px;
}
.min-w-\[24px\] {
  min-width: 24px;
}
.min-w-\[80px\] {
  min-width: 80px;
}
.min-w-full {
  min-width: 100%;
}
.min-w-max {
  min-width: -moz-max-content;
  min-width: max-content;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-\[100\%\] {
  max-width: 100%;
}
.max-w-\[220px\] {
  max-width: 220px;
}
.max-w-\[410px\] {
  max-width: 410px;
}
.max-w-\[42px\] {
  max-width: 42px;
}
.max-w-\[450px\] {
  max-width: 450px;
}
.max-w-\[540px\] {
  max-width: 540px;
}
.max-w-\[558px\] {
  max-width: 558px;
}
.max-w-\[745px\] {
  max-width: 745px;
}
.max-w-\[800px\] {
  max-width: 800px;
}
.max-w-\[86\%\] {
  max-width: 86%;
}
.max-w-\[900px\] {
  max-width: 900px;
}
.max-w-\[977px\] {
  max-width: 977px;
}
.max-w-\[978px\] {
  max-width: 978px;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-screen-lg {
  max-width: 1024px;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink {
  flex-shrink: 1;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.table-fixed {
  table-layout: fixed;
}
.origin-center {
  transform-origin: center;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-\[35deg\] {
  --tw-rotate: -35deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-none {
  transform: none;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.cursor-auto {
  cursor: auto;
}
.cursor-default {
  cursor: default;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-disc {
  list-style-type: disc;
}
.list-none {
  list-style-type: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.place-items-center {
  place-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.\!items-baseline {
  align-items: baseline !important;
}
.items-baseline {
  align-items: baseline;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-items-center {
  justify-items: center;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-\[12px\] {
  gap: 12px;
}
.gap-\[30px\] {
  gap: 30px;
}
.gap-\[8px\] {
  gap: 8px;
}
.gap-x-12 {
  -moz-column-gap: 3rem;
       column-gap: 3rem;
}
.gap-x-5 {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}
.gap-x-\[30px\] {
  -moz-column-gap: 30px;
       column-gap: 30px;
}
.gap-y-4 {
  row-gap: 1rem;
}
.space-x-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2.5rem * var(--tw-space-x-reverse));
  margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-\[24px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(24px * var(--tw-space-x-reverse));
  margin-left: calc(24px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-\[8px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(8px * var(--tw-space-x-reverse));
  margin-left: calc(8px * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-y-\[4px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(4px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4px * var(--tw-space-y-reverse));
}
.space-y-\[8px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(8px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(8px * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.overflow-y-scroll {
  overflow-y: scroll;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis {
  text-overflow: ellipsis;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[16px\] {
  border-radius: 16px;
}
.rounded-\[4px\] {
  border-radius: 4px;
}
.rounded-\[8px\] {
  border-radius: 8px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.rounded-l-\[20px\] {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-r-\[20px\] {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}
.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-bl-\[20px\] {
  border-bottom-left-radius: 20px;
}
.rounded-br-\[20px\] {
  border-bottom-right-radius: 20px;
}
.rounded-tr-\[20px\] {
  border-top-right-radius: 20px;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-\[2px\] {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-\[1px\] {
  border-bottom-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-solid {
  border-style: solid;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.\!border-\[\#76A9FA\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(118 169 250 / var(--tw-border-opacity)) !important;
}
.\!border-red-500 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(240 82 82 / var(--tw-border-opacity)) !important;
}
.border-\[\#057A55\] {
  --tw-border-opacity: 1;
  border-color: rgb(5 122 85 / var(--tw-border-opacity));
}
.border-\[\#9CA3AF\] {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}
.border-\[\#E02424\] {
  --tw-border-opacity: 1;
  border-color: rgb(224 36 36 / var(--tw-border-opacity));
}
.border-\[\#E5E7EB\] {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-\[\#F3F4F6\] {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}
.border-\[\#F43F5E\] {
  --tw-border-opacity: 1;
  border-color: rgb(244 63 94 / var(--tw-border-opacity));
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(28 100 242 / var(--tw-border-opacity));
}
.border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(26 86 219 / var(--tw-border-opacity));
}
.border-blue-divider {
  --tw-border-opacity: 1;
  border-color: rgb(207 219 246 / var(--tw-border-opacity));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}
.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}
.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
}
.border-gray-900 {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity));
}
.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(5 122 85 / var(--tw-border-opacity));
}
.border-primary-blue-1 {
  --tw-border-opacity: 1;
  border-color: rgb(71 118 237 / var(--tw-border-opacity));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(240 82 82 / var(--tw-border-opacity));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(224 36 36 / var(--tw-border-opacity));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-t-\[\#E5E7EB\] {
  --tw-border-opacity: 1;
  border-top-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.\!bg-\[\#76A9FA\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(118 169 250 / var(--tw-bg-opacity)) !important;
}
.\!bg-\[\#D1D5DB\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity)) !important;
}
.\!bg-\[\#F3F4F6\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
}
.\!bg-red-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#0066D3\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 102 211 / var(--tw-bg-opacity));
}
.bg-\[\#0E9F6E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(14 159 110 / var(--tw-bg-opacity));
}
.bg-\[\#389E0D\] {
  --tw-bg-opacity: 1;
  background-color: rgb(56 158 13 / var(--tw-bg-opacity));
}
.bg-\[\#4B5563\] {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
.bg-\[\#7F79F4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(127 121 244 / var(--tw-bg-opacity));
}
.bg-\[\#D1D5DB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.bg-\[\#D5F5F6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(213 245 246 / var(--tw-bg-opacity));
}
.bg-\[\#E2F0F9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(226 240 249 / var(--tw-bg-opacity));
}
.bg-\[\#E5E7EB8C\] {
  background-color: #E5E7EB8C;
}
.bg-\[\#E5E7EB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-\[\#F05252\] {
  --tw-bg-opacity: 1;
  background-color: rgb(240 82 82 / var(--tw-bg-opacity));
}
.bg-\[\#F3F4F6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.bg-\[\#F5F8FA\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 248 250 / var(--tw-bg-opacity));
}
.bg-\[\#F5FFF3\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 255 243 / var(--tw-bg-opacity));
}
.bg-\[\#F9FAFB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.bg-\[\#FE4D01\] {
  --tw-bg-opacity: 1;
  background-color: rgb(254 77 1 / var(--tw-bg-opacity));
}
.bg-\[\#FEECDC\] {
  --tw-bg-opacity: 1;
  background-color: rgb(254 236 220 / var(--tw-bg-opacity));
}
.bg-\[\#FFFAF3\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 250 243 / var(--tw-bg-opacity));
}
.bg-\[\#FFFFFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-active-yellow {
  --tw-bg-opacity: 1;
  background-color: rgb(187 255 41 / var(--tw-bg-opacity));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 131 248 / var(--tw-bg-opacity));
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}
.bg-blue-light {
  --tw-bg-opacity: 1;
  background-color: rgb(230 245 249 / var(--tw-bg-opacity));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(222 247 236 / var(--tw-bg-opacity));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 250 247 / var(--tw-bg-opacity));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 159 110 / var(--tw-bg-opacity));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(5 122 85 / var(--tw-bg-opacity));
}
.bg-green-light {
  --tw-bg-opacity: 1;
  background-color: rgb(239 250 237 / var(--tw-bg-opacity));
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(88 80 236 / var(--tw-bg-opacity));
}
.bg-indigo-light {
  --tw-bg-opacity: 1;
  background-color: rgb(237 241 250 / var(--tw-bg-opacity));
}
.bg-main-background {
  --tw-bg-opacity: 1;
  background-color: rgb(245 248 250 / var(--tw-bg-opacity));
}
.bg-orange-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 138 76 / var(--tw-bg-opacity));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 90 31 / var(--tw-bg-opacity));
}
.bg-orange-light {
  --tw-bg-opacity: 1;
  background-color: rgb(255 239 226 / var(--tw-bg-opacity));
}
.bg-primary-blue-1 {
  --tw-bg-opacity: 1;
  background-color: rgb(71 118 237 / var(--tw-bg-opacity));
}
.bg-progress-green {
  --tw-bg-opacity: 1;
  background-color: rgb(99 204 82 / var(--tw-bg-opacity));
}
.bg-progress-green-light {
  --tw-bg-opacity: 1;
  background-color: rgb(216 255 220 / var(--tw-bg-opacity));
}
.bg-progress-orange {
  --tw-bg-opacity: 1;
  background-color: rgb(255 156 64 / var(--tw-bg-opacity));
}
.bg-progress-orange-light {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 216 / var(--tw-bg-opacity));
}
.bg-purple-divider {
  --tw-bg-opacity: 1;
  background-color: rgb(185 180 215 / var(--tw-bg-opacity));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 242 / var(--tw-bg-opacity));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 82 82 / var(--tw-bg-opacity));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}
.bg-time-box-red {
  --tw-bg-opacity: 1;
  background-color: rgb(241 220 219 / var(--tw-bg-opacity));
}
.bg-time-box-yellow {
  --tw-bg-opacity: 1;
  background-color: rgb(241 231 219 / var(--tw-bg-opacity));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 246 178 / var(--tw-bg-opacity));
}
.bg-yellow-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 233 106 / var(--tw-bg-opacity));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(194 120 3 / var(--tw-bg-opacity));
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-\[url\(\'\/images\/login_page\/Vector\.svg\'\)\] {
  background-image: url('/images/login_page/Vector.svg');
}
.bg-\[url\(\'\/images\/login_page\/bottom-login\.svg\'\)\] {
  background-image: url('/images/login_page/bottom-login.svg');
}
.from-gray-300 {
  --tw-gradient-from: #D1D5DB var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(209 213 219 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ffffff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-gray-300 {
  --tw-gradient-to: #D1D5DB var(--tw-gradient-to-position);
}
.bg-cover {
  background-size: cover;
}
.bg-center {
  background-position: center;
}
.bg-repeat {
  background-repeat: repeat;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-none {
  -o-object-fit: none;
     object-fit: none;
}
.\!p-0 {
  padding: 0px !important;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[10px\] {
  padding: 10px;
}
.p-\[16px\] {
  padding: 16px;
}
.p-\[20px\] {
  padding: 20px;
}
.p-\[25px\] {
  padding: 25px;
}
.\!py-\[0px\] {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[10px\] {
  padding-left: 10px;
  padding-right: 10px;
}
.px-\[12px\] {
  padding-left: 12px;
  padding-right: 12px;
}
.px-\[14px\] {
  padding-left: 14px;
  padding-right: 14px;
}
.px-\[16px\] {
  padding-left: 16px;
  padding-right: 16px;
}
.px-\[20px\] {
  padding-left: 20px;
  padding-right: 20px;
}
.px-\[32px\] {
  padding-left: 32px;
  padding-right: 32px;
}
.px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px;
}
.px-\[6px\] {
  padding-left: 6px;
  padding-right: 6px;
}
.px-\[85px\] {
  padding-left: 85px;
  padding-right: 85px;
}
.px-\[8px\] {
  padding-left: 8px;
  padding-right: 8px;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}
.py-\[12px\] {
  padding-top: 12px;
  padding-bottom: 12px;
}
.py-\[13px\] {
  padding-top: 13px;
  padding-bottom: 13px;
}
.py-\[16px\] {
  padding-top: 16px;
  padding-bottom: 16px;
}
.py-\[24px\] {
  padding-top: 24px;
  padding-bottom: 24px;
}
.py-\[2px\] {
  padding-top: 2px;
  padding-bottom: 2px;
}
.py-\[4px\] {
  padding-top: 4px;
  padding-bottom: 4px;
}
.py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px;
}
.py-\[8px\] {
  padding-top: 8px;
  padding-bottom: 8px;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pb-\[10\%\] {
  padding-bottom: 10%;
}
.pb-\[10px\] {
  padding-bottom: 10px;
}
.pb-\[12px\] {
  padding-bottom: 12px;
}
.pb-\[15px\] {
  padding-bottom: 15px;
}
.pb-\[20px\] {
  padding-bottom: 20px;
}
.pb-\[32px\] {
  padding-bottom: 32px;
}
.pb-\[45px\] {
  padding-bottom: 45px;
}
.pe-4 {
  -webkit-padding-end: 1rem;
          padding-inline-end: 1rem;
}
.pl-0 {
  padding-left: 0px;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-\[15px\] {
  padding-left: 15px;
}
.pl-\[16px\] {
  padding-left: 16px;
}
.pl-\[20px\] {
  padding-left: 20px;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-5 {
  padding-right: 1.25rem;
}
.pr-\[10px\] {
  padding-right: 10px;
}
.pr-\[15px\] {
  padding-right: 15px;
}
.pr-\[16px\] {
  padding-right: 16px;
}
.pr-\[20px\] {
  padding-right: 20px;
}
.pr-\[2px\] {
  padding-right: 2px;
}
.pr-\[5px\] {
  padding-right: 5px;
}
.ps-4 {
  -webkit-padding-start: 1rem;
          padding-inline-start: 1rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-0\.5 {
  padding-top: 0.125rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-\[0px\] {
  padding-top: 0px;
}
.pt-\[12px\] {
  padding-top: 12px;
}
.pt-\[15\%\] {
  padding-top: 15%;
}
.pt-\[16px\] {
  padding-top: 16px;
}
.pt-\[20px\] {
  padding-top: 20px;
}
.pt-\[24px\] {
  padding-top: 24px;
}
.pt-\[25px\] {
  padding-top: 25px;
}
.pt-\[3px\] {
  padding-top: 3px;
}
.pt-\[40px\] {
  padding-top: 40px;
}
.text-left {
  text-align: left;
}
.\!text-center {
  text-align: center !important;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-start {
  text-align: start;
}
.\!text-\[14px\] {
  font-size: 14px !important;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[12px\] {
  font-size: 12px;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-\[16px\] {
  font-size: 16px;
}
.text-\[20px\] {
  font-size: 20px;
}
.text-\[21px\] {
  font-size: 21px;
}
.text-\[24px\] {
  font-size: 24px;
}
.text-\[30px\] {
  font-size: 30px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.ordinal {
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.\!leading-\[21px\] {
  line-height: 21px !important;
}
.\!leading-\[30px\] {
  line-height: 30px !important;
}
.leading-4 {
  line-height: 1rem;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-9 {
  line-height: 2.25rem;
}
.leading-\[130\%\] {
  line-height: 130%;
}
.leading-\[150\%\] {
  line-height: 150%;
}
.leading-\[16px\] {
  line-height: 16px;
}
.leading-\[18px\] {
  line-height: 18px;
}
.leading-\[21px\] {
  line-height: 21px;
}
.leading-\[22px\] {
  line-height: 22px;
}
.leading-\[23\.4px\] {
  line-height: 23.4px;
}
.leading-\[24px\] {
  line-height: 24px;
}
.leading-\[25px\] {
  line-height: 25px;
}
.leading-\[30px\] {
  line-height: 30px;
}
.leading-\[36px\] {
  line-height: 36px;
}
.leading-\[45px\] {
  line-height: 45px;
}
.leading-normal {
  line-height: 1.5;
}
.tracking-\[-0\.24px\] {
  letter-spacing: -0.24px;
}
.tracking-\[0\.14px\] {
  letter-spacing: 0.14px;
}
.tracking-normal {
  letter-spacing: 0em;
}
.tracking-tighter {
  letter-spacing: -0.05em;
}
.\!text-\[\#111928\] {
  --tw-text-opacity: 1 !important;
  color: rgb(17 25 40 / var(--tw-text-opacity)) !important;
}
.\!text-blue-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(28 100 242 / var(--tw-text-opacity)) !important;
}
.\!text-red-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(155 28 28 / var(--tw-text-opacity)) !important;
}
.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.text-\[\#0E9F6E\] {
  --tw-text-opacity: 1;
  color: rgb(14 159 110 / var(--tw-text-opacity));
}
.text-\[\#111928\] {
  --tw-text-opacity: 1;
  color: rgb(17 25 40 / var(--tw-text-opacity));
}
.text-\[\#1D2939\] {
  --tw-text-opacity: 1;
  color: rgb(29 41 57 / var(--tw-text-opacity));
}
.text-\[\#1F2A37\] {
  --tw-text-opacity: 1;
  color: rgb(31 42 55 / var(--tw-text-opacity));
}
.text-\[\#389E0D\] {
  --tw-text-opacity: 1;
  color: rgb(56 158 13 / var(--tw-text-opacity));
}
.text-\[\#4776ED\] {
  --tw-text-opacity: 1;
  color: rgb(71 118 237 / var(--tw-text-opacity));
}
.text-\[\#4B5563\] {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.text-\[\#4D657B\] {
  --tw-text-opacity: 1;
  color: rgb(77 101 123 / var(--tw-text-opacity));
}
.text-\[\#6B7280\] {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-\[\#90853B\] {
  --tw-text-opacity: 1;
  color: rgb(144 133 59 / var(--tw-text-opacity));
}
.text-\[\#9CA3AF\] {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-\[\#D1D5DB\] {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.text-\[\#E02424\] {
  --tw-text-opacity: 1;
  color: rgb(224 36 36 / var(--tw-text-opacity));
}
.text-\[\#E5E7EB\] {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
.text-\[\#F9FAFB\] {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity));
}
.text-\[\#FF8A4C\] {
  --tw-text-opacity: 1;
  color: rgb(255 138 76 / var(--tw-text-opacity));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity));
}
.text-blue-dark {
  --tw-text-opacity: 1;
  color: rgb(75 107 116 / var(--tw-text-opacity));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(14 159 110 / var(--tw-text-opacity));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(5 122 85 / var(--tw-text-opacity));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(3 84 63 / var(--tw-text-opacity));
}
.text-green-dark {
  --tw-text-opacity: 1;
  color: rgb(56 101 48 / var(--tw-text-opacity));
}
.text-indigo-dark {
  --tw-text-opacity: 1;
  color: rgb(38 56 98 / var(--tw-text-opacity));
}
.text-orange-dark {
  --tw-text-opacity: 1;
  color: rgb(163 76 6 / var(--tw-text-opacity));
}
.text-primary-blue-1 {
  --tw-text-opacity: 1;
  color: rgb(71 118 237 / var(--tw-text-opacity));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(224 36 36 / var(--tw-text-opacity));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(200 30 30 / var(--tw-text-opacity));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(155 28 28 / var(--tw-text-opacity));
}
.text-teal-500 {
  --tw-text-opacity: 1;
  color: rgb(6 148 162 / var(--tw-text-opacity));
}
.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(4 116 129 / var(--tw-text-opacity));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(194 120 3 / var(--tw-text-opacity));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.\!placeholder-red-700::-moz-placeholder {
  --tw-placeholder-opacity: 1 !important;
  color: rgb(200 30 30 / var(--tw-placeholder-opacity)) !important;
}
.\!placeholder-red-700::placeholder {
  --tw-placeholder-opacity: 1 !important;
  color: rgb(200 30 30 / var(--tw-placeholder-opacity)) !important;
}
.placeholder-\[\#6B7280\]::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity));
}
.placeholder-\[\#6B7280\]::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity));
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-50 {
  opacity: 0.5;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.\!invert {
  --tw-invert: invert(100%) !important;
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}
.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.sepia {
  --tw-sepia: sepia(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.\!filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter-none {
  filter: none;
}
.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-1000 {
  transition-duration: 1000ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}
.after\:left-\[32\%\]::after {
  content: var(--tw-content);
  left: 32%;
}
.after\:left-\[52\%\]::after {
  content: var(--tw-content);
  left: 52%;
}
.after\:top-\[13px\]::after {
  content: var(--tw-content);
  top: 13px;
}
.after\:top-\[2\.35rem\]::after {
  content: var(--tw-content);
  top: 2.35rem;
}
.after\:top-\[2\.85rem\]::after {
  content: var(--tw-content);
  top: 2.85rem;
}
.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}
.after\:inline-block::after {
  content: var(--tw-content);
  display: inline-block;
}
.after\:h-0::after {
  content: var(--tw-content);
  height: 0px;
}
.after\:h-0\.5::after {
  content: var(--tw-content);
  height: 0.125rem;
}
.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}
.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}
.after\:w-\[115\%\]::after {
  content: var(--tw-content);
  width: 115%;
}
.after\:w-full::after {
  content: var(--tw-content);
  width: 100%;
}
.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}
.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.after\:bg-gray-200::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}
.hover\:border-gray-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.hover\:border-primary-blue-1:hover {
  --tw-border-opacity: 1;
  border-color: rgb(71 118 237 / var(--tw-border-opacity));
}
.hover\:bg-\[\#EBF5FF\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(235 245 255 / var(--tw-bg-opacity));
}
.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(225 239 254 / var(--tw-bg-opacity));
}
.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(195 221 253 / var(--tw-bg-opacity));
}
.hover\:bg-blue-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(118 169 250 / var(--tw-bg-opacity));
}
.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(235 245 255 / var(--tw-bg-opacity));
}
.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}
.hover\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 66 159 / var(--tw-bg-opacity));
}
.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.hover\:bg-orange-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 138 76 / var(--tw-bg-opacity));
}
.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.hover\:text-blue-400:hover {
  --tw-text-opacity: 1;
  color: rgb(118 169 250 / var(--tw-text-opacity));
}
.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity));
}
.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}
.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.hover\:text-green-500:hover {
  --tw-text-opacity: 1;
  color: rgb(14 159 110 / var(--tw-text-opacity));
}
.hover\:no-underline:hover {
  text-decoration-line: none;
}
.hover\:ring-1:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.hover\:ring-4:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.hover\:ring-blue-300:hover {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}
.focus\:z-10:focus {
  z-index: 10;
}
.focus\:border-2:focus {
  border-width: 2px;
}
.focus\:border-\[\#1C64F2\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(28 100 242 / var(--tw-border-opacity));
}
.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity));
}
.focus\:border-green-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(14 159 110 / var(--tw-border-opacity));
}
.focus\:bg-gray-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.focus\:text-\[\#111928\]:focus {
  --tw-text-opacity: 1;
  color: rgb(17 25 40 / var(--tw-text-opacity));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-\[\#1C64F2\]:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(28 100 242 / var(--tw-ring-opacity));
}
.focus\:ring-blue-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(195 221 253 / var(--tw-ring-opacity));
}
.focus\:ring-blue-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}
.focus\:ring-blue-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(118 169 250 / var(--tw-ring-opacity));
}
.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 131 248 / var(--tw-ring-opacity));
}
.focus\:ring-gray-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity));
}
.focus\:ring-offset-0:focus {
  --tw-ring-offset-width: 0px;
}
.disabled\:bg-gray-200:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
:is(.dark .dark\:divide-gray-600) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-divide-opacity));
}
:is(.dark .dark\:border-blue-500) {
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity));
}
:is(.dark .dark\:border-gray-500) {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}
:is(.dark .dark\:border-gray-600) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}
:is(.dark .dark\:border-gray-700) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}
:is(.dark .dark\:border-transparent) {
  border-color: transparent;
}
:is(.dark .dark\:bg-blue-600) {
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}
:is(.dark .dark\:bg-gray-600) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
:is(.dark .dark\:bg-gray-700) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
:is(.dark .dark\:bg-gray-800) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
:is(.dark .dark\:bg-gray-800\/50) {
  background-color: rgb(31 41 55 / 0.5);
}
:is(.dark .dark\:bg-opacity-80) {
  --tw-bg-opacity: 0.8;
}
:is(.dark .dark\:text-blue-400) {
  --tw-text-opacity: 1;
  color: rgb(118 169 250 / var(--tw-text-opacity));
}
:is(.dark .dark\:text-blue-500) {
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity));
}
:is(.dark .dark\:text-gray-200) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
:is(.dark .dark\:text-gray-300) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
:is(.dark .dark\:text-gray-400) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
:is(.dark .dark\:text-gray-900) {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
:is(.dark .dark\:text-red-500) {
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity));
}
:is(.dark .dark\:text-white) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
:is(.dark .dark\:placeholder-gray-400)::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
:is(.dark .dark\:placeholder-gray-400)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
:is(.dark .dark\:ring-offset-gray-700) {
  --tw-ring-offset-color: #374151;
}
:is(.dark .dark\:ring-offset-gray-800) {
  --tw-ring-offset-color: #1F2937;
}
:is(.dark .dark\:hover\:border-gray-600:hover) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}
:is(.dark .dark\:hover\:bg-blue-700:hover) {
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}
:is(.dark .dark\:hover\:bg-blue-800:hover) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 66 159 / var(--tw-bg-opacity));
}
:is(.dark .dark\:hover\:bg-gray-600:hover) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
:is(.dark .dark\:hover\:bg-gray-700:hover) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
:is(.dark .dark\:hover\:bg-gray-800:hover) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
:is(.dark .dark\:hover\:text-blue-500:hover) {
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity));
}
:is(.dark .dark\:hover\:text-gray-300:hover) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
:is(.dark .dark\:hover\:text-white:hover) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
:is(.dark .dark\:focus\:border-blue-500:focus) {
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity));
}
:is(.dark .dark\:focus\:ring-blue-500:focus) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 131 248 / var(--tw-ring-opacity));
}
:is(.dark .dark\:focus\:ring-blue-600:focus) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(28 100 242 / var(--tw-ring-opacity));
}
:is(.dark .dark\:focus\:ring-blue-800:focus) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 66 159 / var(--tw-ring-opacity));
}
:is(.dark .dark\:focus\:ring-gray-600:focus) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(75 85 99 / var(--tw-ring-opacity));
}
:is(.dark .dark\:focus\:ring-offset-gray-700:focus) {
  --tw-ring-offset-color: #374151;
}
:is(.dark .dark\:focus\:ring-offset-gray-800:focus) {
  --tw-ring-offset-color: #1F2937;
}
:is(.dark .peer:focus ~ .dark\:peer-focus\:ring-blue-800) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 66 159 / var(--tw-ring-opacity));
}
@media (min-width: 640px) {

  .sm\:ml-52 {
    margin-left: 13rem;
  }

  .sm\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}
@media (min-width: 768px) {

  .md\:inset-0 {
    inset: 0px;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  @media not all and (min-width: 1024px) {

    .md\:max-lg\:hidden {
      display: none;
    }
  }
}
@media (min-width: 1024px) {

  .lg\:fixed {
    position: fixed;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mt-1 {
    margin-top: 0.25rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline-block {
    display: inline-block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-5 {
    height: 1.25rem;
  }

  .lg\:h-\[120px\] {
    height: 120px;
  }

  .lg\:h-\[82px\] {
    height: 82px;
  }

  .lg\:w-4\/12 {
    width: 33.333333%;
  }

  .lg\:w-5 {
    width: 1.25rem;
  }

  .lg\:w-8\/12 {
    width: 66.666667%;
  }

  .lg\:w-\[120px\] {
    width: 120px;
  }

  .lg\:w-\[145px\] {
    width: 145px;
  }

  .lg\:w-\[300px\] {
    width: 300px;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:gap-3 {
    gap: 0.75rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-y-2 {
    row-gap: 0.5rem;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:pl-3 {
    padding-left: 0.75rem;
  }

  .lg\:pt-6 {
    padding-top: 1.5rem;
  }
}
@media (min-width: 1280px) {

  .xl\:mr-64 {
    margin-right: 16rem;
  }

  .xl\:table-cell {
    display: table-cell;
  }

  .xl\:gap-x-20 {
    -moz-column-gap: 5rem;
         column-gap: 5rem;
  }

  .xl\:gap-y-5 {
    row-gap: 1.25rem;
  }

  .xl\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .xl\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .xl\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
